{"name": "elevenlabs-clone-frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev --turbo", "inngest-dev": "npx inngest-cli@latest dev -u http://localhost:3000/api/inngest", "postinstall": "prisma generate", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache"}, "dependencies": {"@auth/prisma-adapter": "2.7.2", "@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@hookform/resolvers": "^4.1.3", "@prisma/client": "^5.14.0", "@t3-oss/env-nextjs": "^0.10.1", "bcryptjs": "^3.0.2", "geist": "^1.3.0", "inngest": "^3.32.9", "next": "^15.0.1", "next-auth": "5.0.0-beta.25", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "zod": "^3.23.3", "zustand": "^5.0.3"}, "devDependencies": {"@types/eslint": "^8.56.10", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "eslint": "^8.57.0", "eslint-config-next": "^15.0.1", "postcss": "^8.4.39", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "prisma": "^5.14.0", "tailwindcss": "^3.4.3", "typescript": "^5.5.3"}, "ct3aMetadata": {"initVersion": "7.38.1"}, "packageManager": "npm@10.2.4"}