# Git-related files in subdirectories
*/*/.git/
*/*/.gitignore
*/*/.gitmodules
*/*/.gitattributes

# Python-related ignores
**/__pycache__/
**/*.py[cod]
**/*$py.class
**/*.so
**/.Python
**/env/
**/build/
**/develop-eggs/
**/dist/
**/downloads/
**/eggs/
**/.eggs/
**/lib64/
**/parts/
**/sdist/
**/var/
**/wheels/
**/*.egg-info/
**/.installed.cfg
**/*.egg
**/.env
**/.venv
**/venv/
**/ENV/
**/.ipynb_checkpoints/

# Node.js related ignores
**/node_modules/
**/npm-debug.log
**/yarn-debug.log*
**/yarn-error.log*
**/.pnpm-debug.log*

# Next.js specific
**/.next/
**/out/
**/.vercel
**/.turbo

# AWS credentials and config
**/.aws/
**/aws.json
**/.aws-credentials

# Docker related
**/docker-compose.override.yml

# Model files and large data (adjust as needed)
**/*.pth
**/*.pt
**/*.pkl
**/*.h5
**/*.onnx
**/*.safetensors
**/*.bin
**/*.wav
**/*.mp3
**/*.mp4
**/*.webm

# IDE specific files
**/.idea/
**/.vscode/
**/.vs/
**/*.swp
**/*.swo
**/*~

# Mac specific files
**/.DS_Store
**/.AppleDouble
**/.LSOverride

# Windows specific files
**/Thumbs.db
**/ehthumbs.db
**/Desktop.ini

# Temporary files
**/tmp/
**/temp/

# Logs
**/logs/
**/*.log