version: "3.8"

services:
  styletts2-api:
    image: ecreimagehere
    ports:
      - "8000:8000"
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  seedvc-api:
    image: ecreimagehere
    ports:
      - "8001:8000"
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  make-an-audio-api:
    image: ecreimagehere
    ports:
      - "8002:8000"
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
