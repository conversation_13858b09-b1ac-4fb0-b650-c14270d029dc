FROM pytorch/pytorch:2.1.0-cuda11.8-cudnn8-runtime

WORKDIR /app

ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Etc/UTC

RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    python3-dev \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY configs ./configs
COPY ldm ./ldm
COPY vocoder ./vocoder
COPY gen_wav.py ./
COPY api.py ./
COPY useful_ckpts ./useful_ckpts

ENV PYTHONPATH=/app

ENV AWS_REGION=us-east-1
ENV S3_BUCKET=elevenlabs-clone
ENV S3_PREFIX=make-an-audio-outputs
ENV API_KEY=12345

EXPOSE 8000

CMD ["uvicorn", "api:app", "--host", "0.0.0.0", "--port", "8000"]