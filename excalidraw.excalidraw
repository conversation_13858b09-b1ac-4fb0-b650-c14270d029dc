{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"id": "AO6Q1cqmHvnaCu0rumvYN", "type": "text", "x": 5127.918866646347, "y": 1137.2177709924363, "width": 154.99989318847656, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a1", "roundness": null, "seed": 2038997262, "version": 1146, "versionNonce": 1993925577, "isDeleted": false, "boundElements": [], "updated": 1742298167950, "link": null, "locked": false, "text": "<PERSON>er compose", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "<PERSON>er compose", "autoResize": true, "lineHeight": 1.25}, {"id": "VK3wTbBhbtu19_MNXF3Y2", "type": "rectangle", "x": 5043.167234691345, "y": 1237.7163792949582, "width": 235.39604640607737, "height": 117.91092103601353, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a2", "roundness": {"type": 3}, "seed": 1922779214, "version": 806, "versionNonce": 461079209, "isDeleted": false, "boundElements": [{"type": "text", "id": "PWAlLYkGhByRmSpv2zEaU"}], "updated": 1742298167950, "link": null, "locked": false}, {"id": "PWAlLYkGhByRmSpv2zEaU", "type": "text", "x": 5095.815308248387, "y": 1271.671839812965, "width": 130.0998992919922, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a2V", "roundness": null, "seed": 1354336590, "version": 733, "versionNonce": 338118025, "isDeleted": false, "boundElements": [], "updated": 1742298167950, "link": null, "locked": false, "text": "<container>\nstyletts2-api", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "VK3wTbBhbtu19_MNXF3Y2", "originalText": "<container>\nstyletts2-api", "autoResize": true, "lineHeight": 1.25}, {"id": "2xPMp3mwlePDg5qyCZmLn", "type": "text", "x": 5326.968462107552, "y": 1280.0651708279174, "width": 57.83998107910156, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "a8", "roundness": null, "seed": 1747851406, "version": 605, "versionNonce": 1276619881, "isDeleted": false, "boundElements": [], "updated": 1742298167950, "link": null, "locked": false, "text": ":8000", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": ":8000", "autoResize": true, "lineHeight": 1.25}, {"id": "zp2okWLfmmdt_sqZbWDSE", "type": "rectangle", "x": 4990.81210853923, "y": 1185.961736227541, "width": 438.57479620107284, "height": 530.0024040419335, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aB", "roundness": {"type": 3}, "seed": 1195023890, "version": 1103, "versionNonce": 991442761, "isDeleted": false, "boundElements": [], "updated": 1742298167950, "link": null, "locked": false}, {"id": "sqD7x7LsTtQONkJNwHaX5", "type": "text", "x": -810.646109477681, "y": 917.2169605334904, "width": 296.53192138671875, "height": 45, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aC", "roundness": null, "seed": 976288718, "version": 741, "versionNonce": 1401185833, "isDeleted": false, "boundElements": [], "updated": 1742298167950, "link": null, "locked": false, "text": "Feature overview", "fontSize": 36, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Feature overview", "autoResize": true, "lineHeight": 1.25}, {"id": "Td_Fb8i9LcQB4IbZlQ6-k", "type": "rectangle", "x": 5042.371260754523, "y": 1394.016602771389, "width": 235.39604640607737, "height": 117.91092103601353, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aD", "roundness": {"type": 3}, "seed": 1769252434, "version": 888, "versionNonce": 810006793, "isDeleted": false, "boundElements": [{"type": "text", "id": "yKe-Lk5LToWLlzk_AGcev"}], "updated": 1742298167950, "link": null, "locked": false}, {"id": "yKe-Lk5LToWLlzk_AGcev", "type": "text", "x": 5104.289323325237, "y": 1427.9720632893957, "width": 111.55992126464844, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aE", "roundness": null, "seed": 1550846994, "version": 801, "versionNonce": 48082921, "isDeleted": false, "boundElements": [], "updated": 1742298167950, "link": null, "locked": false, "text": "<container>\nseed-vc-api", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "Td_Fb8i9LcQB4IbZlQ6-k", "originalText": "<container>\nseed-vc-api", "autoResize": true, "lineHeight": 1.25}, {"id": "5Xq9kAv4SaavoKVj9SJla", "type": "text", "x": 5326.182909758457, "y": 1436.469999909632, "width": 53.0999755859375, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aF", "roundness": null, "seed": 779663826, "version": 688, "versionNonce": 1095879369, "isDeleted": false, "boundElements": [], "updated": 1742298167950, "link": null, "locked": false, "text": ":8001", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": ":8001", "autoResize": true, "lineHeight": 1.25}, {"id": "uTqMIsw4cbyVoVwIUf41m", "type": "rectangle", "x": 5043.617442992662, "y": 1548.3190273950906, "width": 235.39604640607737, "height": 117.91092103601353, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aG", "roundness": {"type": 3}, "seed": 1426169934, "version": 954, "versionNonce": 339708329, "isDeleted": false, "boundElements": [{"type": "text", "id": "qj5xeSTbFZ6PodUWcJZgs"}], "updated": 1742298167950, "link": null, "locked": false}, {"id": "qj5xeSTbFZ6PodUWcJZgs", "type": "text", "x": 5078.345548898337, "y": 1582.2744879130973, "width": 165.93983459472656, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aH", "roundness": null, "seed": 1422157454, "version": 867, "versionNonce": 80899209, "isDeleted": false, "boundElements": [], "updated": 1742298167950, "link": null, "locked": false, "text": "<container>\nmake-a-sound-api", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "uTqMIsw4cbyVoVwIUf41m", "originalText": "<container>\nmake-a-sound-api", "autoResize": true, "lineHeight": 1.25}, {"id": "h5gKVgRmciOY-iHOvZwJA", "type": "text", "x": 5327.523276014153, "y": 1590.5914982955833, "width": 58.55998229980469, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aI", "roundness": null, "seed": 1423205582, "version": 737, "versionNonce": 1791357801, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": ":8002", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": ":8002", "autoResize": true, "lineHeight": 1.25}, {"id": "W1sk15vYyldbwupkjphnZ", "type": "text", "x": 4943.486776430542, "y": 904.5889443817537, "width": 281.0519104003906, "height": 45, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aJ", "roundness": null, "seed": 1069636366, "version": 1258, "versionNonce": 442867273, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "API Deployment", "fontSize": 36, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "API Deployment", "autoResize": true, "lineHeight": 1.25}, {"id": "hqQLIgqpReHqGbWZw5xZg", "type": "rectangle", "x": 4939.439436148083, "y": 1064.7597569178952, "width": 537.0320586656655, "height": 697.8906719608204, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aK", "roundness": {"type": 3}, "seed": 1618497102, "version": 778, "versionNonce": 1384967465, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false}, {"id": "EH_2M8VQXW5gFttDizOPr", "type": "text", "x": 5157.7229795161475, "y": 1023.9916686316606, "width": 90.39994812011719, "height": 25, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aO", "roundness": null, "seed": 1920915470, "version": 434, "versionNonce": 1789353993, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "AWS EC2", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "AWS EC2", "autoResize": true, "lineHeight": 1.25}, {"id": "5nc1A-RMqAT-Fp_bfkpNz", "type": "text", "x": 6342.986422295484, "y": 894.2534427190642, "width": 159.11993408203125, "height": 45, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aP", "roundness": null, "seed": 573660242, "version": 2102, "versionNonce": 1085306601, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Frontend", "fontSize": 36, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Frontend", "autoResize": true, "lineHeight": 1.25}, {"id": "qNOzuNZ4yxajefK4-Idc5", "type": "text", "x": 6386.807133230722, "y": 1352.0723823771934, "width": 55.539947509765625, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aS", "roundness": null, "seed": 537536210, "version": 1093, "versionNonce": 1731441097, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Vercel", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Vercel", "autoResize": true, "lineHeight": 1.25}, {"id": "4h0tLtkptlCs_E3FiOzOG", "type": "rectangle", "x": 6289.41309281476, "y": 1399.0561565704922, "width": 252.32250901245925, "height": 136.19888840478893, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aT", "roundness": {"type": 3}, "seed": 447126478, "version": 1185, "versionNonce": 323784873, "isDeleted": false, "boundElements": [{"type": "text", "id": "-KeIGU8hLGk_wEtO_NgyG"}], "updated": 1742298167951, "link": null, "locked": false}, {"id": "-KeIGU8hLGk_wEtO_NgyG", "type": "text", "x": 6381.09436685224, "y": 1454.6556007728866, "width": 68.9599609375, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aU", "roundness": null, "seed": 591262222, "version": 1166, "versionNonce": 1252430729, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Next.js", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "4h0tLtkptlCs_E3FiOzOG", "originalText": "Next.js", "autoResize": true, "lineHeight": 1.25}, {"id": "oAARcdtOD4LAwDVa2cemV", "type": "line", "x": 6288.782246063643, "y": 1463.949604246169, "width": 806.4242160823924, "height": 174.99005719478714, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aY", "roundness": {"type": 2}, "seed": 1469721422, "version": 458, "versionNonce": 1367161449, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "points": [[0, 0], [-806.4242160823924, -174.99005719478714]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "s4KsZr5EozvRB6ZrOuzvD", "type": "line", "x": 6288.8600481852, "y": 1476.624921951885, "width": 807.1952186528742, "height": 11.219641285250418, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aZ", "roundness": {"type": 2}, "seed": 1406416658, "version": 539, "versionNonce": 1486619977, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "points": [[0, 0], [-807.1952186528742, -11.219641285250418]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "1oX2SHD6IdDLkKkhsGONB", "type": "line", "x": 6288.422063523348, "y": 1488.5268348925958, "width": 806.4867609677385, "height": 114.03614979683925, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aa", "roundness": {"type": 2}, "seed": 162106002, "version": 516, "versionNonce": 1801352233, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "points": [[0, 0], [-806.4867609677385, 114.03614979683925]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "Q_l-OuvG6yQ6nD_VeGbdZ", "type": "text", "x": 5723.327120329249, "y": 1303.2810803199056, "width": 457.5397033691406, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ab", "roundness": null, "seed": 1375051090, "version": 486, "versionNonce": 498075401, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Text-to-Speech: request (EC2 public IP):8000", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Text-to-Speech: request (EC2 public IP):8000", "autoResize": true, "lineHeight": 1.25}, {"id": "wdFgCSc-bTnwWZ2KCxj3a", "type": "text", "x": 5503.664687170196, "y": 1425.5870717786825, "width": 329.4997863769531, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ac", "roundness": null, "seed": 394930830, "version": 544, "versionNonce": 2047622633, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Voice conversion: request IP:8001", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Voice conversion: request IP:8001", "autoResize": true, "lineHeight": 1.25}, {"id": "58_LpsZdbzraIdptvakOr", "type": "text", "x": 5702.277322546721, "y": 1586.7435481855357, "width": 223.5798797607422, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ad", "roundness": null, "seed": 569073038, "version": 779, "versionNonce": 796825801, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Text-to-SFX: IP:8002", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Text-to-SFX: IP:8002", "autoResize": true, "lineHeight": 1.25}, {"id": "nGlAn8vTX__f-57LQpOCP", "type": "text", "x": 4960.054740310514, "y": 1084.6878894188328, "width": 91.2999267578125, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ae", "roundness": null, "seed": 97516626, "version": 437, "versionNonce": 2141362089, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "G5.<PERSON><PERSON>ge", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "G5.<PERSON><PERSON>ge", "autoResize": true, "lineHeight": 1.25}, {"id": "3uu4E4_BkqLoQT1Nz85pw", "type": "text", "x": -808.4172483015934, "y": 1003.6675463122965, "width": 157.99990844726562, "height": 150, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ag", "roundness": null, "seed": 1012368974, "version": 292, "versionNonce": 1822669449, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Text to speech:\nStyleTTS2\n\nFinetune to\ngenerate\nspecific voice", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Text to speech:\nStyleTTS2\n\nFinetune to\ngenerate\nspecific voice", "autoResize": true, "lineHeight": 1.25}, {"id": "S0QtR_9lSL2dUCWllWZrg", "type": "text", "x": -408.28044078945413, "y": 1001.9085119454774, "width": 162.55987548828125, "height": 150, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ah", "roundness": null, "seed": 652448590, "version": 498, "versionNonce": 658342249, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Voice conversion:\nseed-vc\n\nFinetune to be\nable to convert\nto specific voice", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Voice conversion:\nseed-vc\n\nFinetune to be\nable to convert\nto specific voice", "autoResize": true, "lineHeight": 1.25}, {"id": "CujZYHUkN1ctYMlOO_PwY", "type": "text", "x": -32.30650461287996, "y": 1006.6212483821821, "width": 229.4798583984375, "height": 100, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ai", "roundness": null, "seed": 431469582, "version": 648, "versionNonce": 1018915913, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Text to sound effects:\nmake-an-audio\n\nNo finetuning.", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Text to sound effects:\nmake-an-audio\n\nNo finetuning.", "autoResize": true, "lineHeight": 1.25}, {"id": "aT2FwcLHmNb3PMdyl9eYh", "type": "line", "x": -803.859765671076, "y": 1064.139207632171, "width": 1095.2421952189657, "height": 1.1163072545396062, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ak", "roundness": {"type": 2}, "seed": 618502158, "version": 654, "versionNonce": 83426089, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "points": [[0, 0], [1095.2421952189657, 1.1163072545396062]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "mype4dg-sYIeNXbQTmT8R", "type": "rectangle", "x": 630.7698294973566, "y": 1050.5232776038165, "width": 209.00107023124406, "height": 78.51053076119933, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "am", "roundness": {"type": 3}, "seed": 217166290, "version": 470, "versionNonce": 1348578825, "isDeleted": false, "boundElements": [{"type": "text", "id": "MhiHryX9jdJcoC8ACVr6I"}], "updated": 1742298167951, "link": null, "locked": false}, {"id": "MhiHryX9jdJcoC8ACVr6I", "type": "text", "x": 715.4003771251856, "y": 1077.2785429844162, "width": 39.73997497558594, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "amV", "roundness": null, "seed": 568863054, "version": 363, "versionNonce": 1441858793, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "IAM", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "mype4dg-sYIeNXbQTmT8R", "originalText": "IAM", "autoResize": true, "lineHeight": 1.25}, {"id": "ae9rz4luhkTWiuV361Els", "type": "text", "x": 631.76582218136, "y": 911.6057050860941, "width": 75.02398681640625, "height": 45, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ao", "roundness": null, "seed": 209697166, "version": 813, "versionNonce": 1630699465, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "AWS", "fontSize": 36, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "AWS", "autoResize": true, "lineHeight": 1.25}, {"id": "963L-zvrreepzALzaTlC-", "type": "rectangle", "x": 1074.253571239814, "y": 1048.4784662541215, "width": 209.00107023124406, "height": 78.51053076119933, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "aq", "roundness": {"type": 3}, "seed": 1451232206, "version": 532, "versionNonce": 1078042281, "isDeleted": false, "boundElements": [{"type": "text", "id": "3iWQGSdP6Jn3oGSrZ5IU7"}], "updated": 1742298167951, "link": null, "locked": false}, {"id": "3iWQGSdP6Jn3oGSrZ5IU7", "type": "text", "x": 1158.0341203935218, "y": 1075.2337316347212, "width": 41.4**************, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ar", "roundness": null, "seed": 40460814, "version": 427, "versionNonce": 1909055881, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "ECR", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "963L-zvrreepzALzaTlC-", "originalText": "ECR", "autoResize": true, "lineHeight": 1.25}, {"id": "-pRuR28IAAJKF7VWVT3Ze", "type": "rectangle", "x": 1423.9668057612153, "y": 1045.764478511384, "width": 209.00107023124406, "height": 78.51053076119933, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "as", "roundness": {"type": 3}, "seed": 753770574, "version": 615, "versionNonce": 319211625, "isDeleted": false, "boundElements": [{"type": "text", "id": "6YUgvRU3CqdBVMaCUOQZ2"}], "updated": 1742298167951, "link": null, "locked": false}, {"id": "6YUgvRU3CqdBVMaCUOQZ2", "type": "text", "x": 1508.1073478958801, "y": 1072.5197438919836, "width": 40.71998596191406, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "at", "roundness": null, "seed": 1328572046, "version": 514, "versionNonce": 230937417, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "EC2", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "-pRuR28IAAJKF7VWVT3Ze", "originalText": "EC2", "autoResize": true, "lineHeight": 1.25}, {"id": "U64Pq-mGcWsrIG1RUuvOX", "type": "rectangle", "x": 1747.1141834825687, "y": 1044.7004833799945, "width": 209.00107023124406, "height": 78.51053076119933, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "au", "roundness": {"type": 3}, "seed": 1099110286, "version": 704, "versionNonce": 106725929, "isDeleted": false, "boundElements": [{"type": "text", "id": "YEnXkpJ1xk8WYmj4xyi9-"}], "updated": 1742298167951, "link": null, "locked": false}, {"id": "YEnXkpJ1xk8WYmj4xyi9-", "type": "text", "x": 1839.314730805222, "y": 1071.4557487605941, "width": 24.5999755859375, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "av", "roundness": null, "seed": 2023028174, "version": 606, "versionNonce": 163337481, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "S3", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "U64Pq-mGcWsrIG1RUuvOX", "originalText": "S3", "autoResize": true, "lineHeight": 1.25}, {"id": "zk8XIqF958Pu5TN9xOmtP", "type": "rectangle", "x": 2054.2885172395, "y": 1042.4070031520534, "width": 209.00107023124406, "height": 78.51053076119933, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "ay", "roundness": {"type": 3}, "seed": 241378962, "version": 834, "versionNonce": 2069810153, "isDeleted": false, "boundElements": [{"type": "text", "id": "7lzVbZGb6V2pfNiiVEZ3o"}], "updated": 1742298167951, "link": null, "locked": false}, {"id": "7lzVbZGb6V2pfNiiVEZ3o", "type": "text", "x": 2122.9090779898875, "y": 1069.1622685326531, "width": 71.75994873046875, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "az", "roundness": null, "seed": 405046354, "version": 751, "versionNonce": 1129277129, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Quotas", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "zk8XIqF958Pu5TN9xOmtP", "originalText": "Quotas", "autoResize": true, "lineHeight": 1.25}, {"id": "T8Q0jcBcIHxVhD1tOWiqh", "type": "text", "x": 635.4323287030006, "y": 1158.3092646520472, "width": 191.11984252929688, "height": 225, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b00", "roundness": null, "seed": 586895506, "version": 187, "versionNonce": 1009362345, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "User:\nstyletts2-api\n\nWith policy:\nstyletts2-api-policy\n\nAllows:\nList, read, write\ns3:::elevenlabs-clone", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "User:\nstyletts2-api\n\nWith policy:\nstyletts2-api-policy\n\nAllows:\nList, read, write\ns3:::elevenlabs-clone", "autoResize": true, "lineHeight": 1.25}, {"id": "nMQHjZqyUH6FulO_60eZy", "type": "text", "x": 639.1667460448348, "y": 1495.831673482861, "width": 386.5596923828125, "height": 200, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b01", "roundness": null, "seed": 1615474194, "version": 504, "versionNonce": 1629030537, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Role:\nelevenlabs-clone-ec2\n\nWith permissions:\nAmazonEC2ContainerRegistryFullAccess\nAmazonS3FullAccess\nPut, get, list to s3:::elevenlabs-clone\n", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Role:\nelevenlabs-clone-ec2\n\nWith permissions:\nAmazonEC2ContainerRegistryFullAccess\nAmazonS3FullAccess\nPut, get, list to s3:::elevenlabs-clone\n", "autoResize": true, "lineHeight": 1.25}, {"id": "CDG-6iUrOyDWOagu_YvVu", "type": "line", "x": 635.5656913446055, "y": 1479.1445962894936, "width": 397.20551645780796, "height": 1.2921332828366303, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b03", "roundness": {"type": 2}, "seed": 1856831570, "version": 496, "versionNonce": 1004081001, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "points": [[0, 0], [397.20551645780796, 1.2921332828366303]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "EZSsGq3AIIx_PsvrXCi8t", "type": "text", "x": 1079.9496152628353, "y": 1156.1126755854743, "width": 165.93983459472656, "height": 175, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b06", "roundness": null, "seed": 1658658386, "version": 193, "versionNonce": 313866825, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "styletts2-ft\nstyletts2-api\n\nseed-vc-ft\nseed-vc-api\n\nmake-a-sound-api", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "styletts2-ft\nstyletts2-api\n\nseed-vc-ft\nseed-vc-api\n\nmake-a-sound-api", "autoResize": true, "lineHeight": 1.25}, {"id": "J8oWO6ClqOYuQmQAtlhSG", "type": "line", "x": 1078.0141394301272, "y": 1220.2582639902555, "width": 203.949946870541, "height": 0.004546263945712781, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b07", "roundness": {"type": 2}, "seed": 1417136530, "version": 206, "versionNonce": 1451821353, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "points": [[0, 0], [203.949946870541, 0.004546263945712781]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "bQBp4BiKhtzOEWRIgq8LI", "type": "line", "x": 1077.645892050521, "y": 1294.1350531087837, "width": 208.8144492924995, "height": 0.3909786993349371, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b08", "roundness": {"type": 2}, "seed": 1664736786, "version": 183, "versionNonce": 1354759177, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "points": [[0, 0], [208.8144492924995, 0.3909786993349371]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "w2DFxqv8uI9pvK65mRtyE", "type": "text", "x": 1428.50153236091, "y": 1153.8611393128842, "width": 186.57981872558594, "height": 150, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b09", "roundness": null, "seed": 166520018, "version": 534, "versionNonce": 290820841, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Finetuning instance\n\nG5.Xlarge\nAMI GPU PyTorch\nAmazon Linux\n100GB storage", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Finetuning instance\n\nG5.Xlarge\nAMI GPU PyTorch\nAmazon Linux\n100GB storage", "autoResize": true, "lineHeight": 1.25}, {"id": "I9Oe3-r1ECfdGFlii134g", "type": "text", "x": 1432.9110441021721, "y": 1357.7789916999234, "width": 197.93984985351562, "height": 350, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0B", "roundness": null, "seed": 893861454, "version": 769, "versionNonce": 483741129, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Deployment instance\n\nG5.Xlarge\nAMI GPU PyTorch\nAmazon Linux\n100GB storage\n\nAllow TCP\nPort 8000 - 8002\nFrom anywhere\n\nAllow HTTP\nPort 80\nFrom anywhere", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Deployment instance\n\nG5.Xlarge\nAMI GPU PyTorch\nAmazon Linux\n100GB storage\n\nAllow TCP\nPort 8000 - 8002\nFrom anywhere\n\nAllow HTTP\nPort 80\nFrom anywhere", "autoResize": true, "lineHeight": 1.25}, {"id": "PX2UovniGYebc200xB758", "type": "line", "x": 1431.1663524057205, "y": 1332.331641488336, "width": 201.20210315133068, "height": 1.173473390347226, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0C", "roundness": {"type": 2}, "seed": 444211214, "version": 247, "versionNonce": 1515891881, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "points": [[0, 0], [201.20210315133068, -1.173473390347226]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "NYDUREsFcE45gftgFY_u0", "type": "line", "x": 1434.236863056539, "y": 1518.984325814695, "width": 99.62938571104655, "height": 0.2728138773736646, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0D", "roundness": {"type": 2}, "seed": 699650126, "version": 499, "versionNonce": 1100033929, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "points": [[0, 0], [99.62938571104655, -0.2728138773736646]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "oPtITZxX_NZYRa8SVAIy_", "type": "line", "x": 1433.2907402526741, "y": 1618.43012318167, "width": 99.62938571104655, "height": 0.2728138773736646, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0E", "roundness": {"type": 2}, "seed": 1733554130, "version": 573, "versionNonce": 1219778153, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "points": [[0, 0], [99.62938571104655, -0.2728138773736646]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "Nx_r5PtjlBJenZd6iLJqY", "type": "line", "x": 1434.8023987405757, "y": 1396.0295606687707, "width": 99.62938571104655, "height": 0.2728138773736646, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0F", "roundness": {"type": 2}, "seed": 1519587154, "version": 674, "versionNonce": 311402825, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "points": [[0, 0], [99.62938571104655, -0.2728138773736646]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "e5gv2luLLicee46-TXluM", "type": "line", "x": 1428.0612616333065, "y": 1193.266512779322, "width": 99.62938571104655, "height": 0.2728138773736646, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0G", "roundness": {"type": 2}, "seed": 2104395214, "version": 688, "versionNonce": 1551798313, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "points": [[0, 0], [99.62938571104655, -0.2728138773736646]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "6UpeLC9PTXviAYqpMvzA8", "type": "text", "x": 1749.0527805050194, "y": 1152.4664674551973, "width": 233.19979858398438, "height": 225, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0H", "roundness": null, "seed": 2018631954, "version": 813, "versionNonce": 745317129, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "elevenlabs-clone\n/styletts2-models\n/seed-vc-models\n\n/styletts2-outputs\n/seedvc-outputs\n/make-an-audio-outputs\n\n/seedvc-audio-uploads", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "elevenlabs-clone\n/styletts2-models\n/seed-vc-models\n\n/styletts2-outputs\n/seedvc-outputs\n/make-an-audio-outputs\n\n/seedvc-audio-uploads", "autoResize": true, "lineHeight": 1.25}, {"id": "8ZbY8-1Kp5xhkx32tOyPv", "type": "text", "x": 2063.365624621736, "y": 1156.8742055468517, "width": 442.6197204589844, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0J", "roundness": null, "seed": 1498760078, "version": 164, "versionNonce": 1054169577, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Amazon Elastic Compute Cloud (Amazon EC2)\nRunning On-Demand G and VT instances\n4 vCPU's", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Amazon Elastic Compute Cloud (Amazon EC2)\nRunning On-Demand G and VT instances\n4 vCPU's", "autoResize": true, "lineHeight": 1.25}, {"id": "4BGGr5RGiMrAUPyJ8Fe5z", "type": "text", "x": 641.6212583312195, "y": 1680.1818585668282, "width": 356.3797912597656, "height": 75, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0K", "roundness": null, "seed": 1067129170, "version": 785, "versionNonce": 885273801, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Purpose: attach to EC2 instance to\ngive it permissions corresponding\nto the role", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Purpose: attach to EC2 instance to\ngive it permissions corresponding\nto the role", "autoResize": true, "lineHeight": 1.25}, {"id": "Ob5rHFJQ8Ri8UxydsWIoo", "type": "text", "x": 637.7244362649242, "y": 1395.0122730598907, "width": 325.7997741699219, "height": 50, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "#ffd8a8", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0L", "roundness": null, "seed": 423717070, "version": 736, "versionNonce": 30716841, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Purpose: use locally when program\nneeds to upload to S3 bucket", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Purpose: use locally when program\nneeds to upload to S3 bucket", "autoResize": true, "lineHeight": 1.25}, {"id": "6zbeExBxYCwMz9Wgr5J7K", "type": "text", "x": 6972.0106134647995, "y": 891.8805709706861, "width": 78.4439697265625, "height": 45, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0M", "roundness": null, "seed": 861056274, "version": 2158, "versionNonce": 439506569, "isDeleted": false, "boundElements": [], "updated": 1742298167951, "link": null, "locked": false, "text": "Flow", "fontSize": 36, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Flow", "autoResize": true, "lineHeight": 1.25}, {"id": "bw38CdnQs_uk8ZwclH5p5", "type": "rectangle", "x": 6987.363904996287, "y": 1201.6738480090312, "width": 204.71253913401046, "height": 78.72014580735438, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0P", "roundness": {"type": 3}, "seed": 2062147790, "version": 419, "versionNonce": 1620614505, "isDeleted": false, "boundElements": [{"type": "text", "id": "6EgE9Z9VYoEaE3Zqq79KQ"}, {"id": "_TIoTwHIoWV2AeEkgVy4A", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "6EgE9Z9VYoEaE3Zqq79KQ", "type": "text", "x": 7036.030202639464, "y": 1228.5339209127085, "width": 107.37994384765625, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0PV", "roundness": null, "seed": 222692750, "version": 385, "versionNonce": 1414727753, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Enter text", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "bw38CdnQs_uk8ZwclH5p5", "originalText": "Enter text", "autoResize": true, "lineHeight": 1.25}, {"id": "VzBWXpj40g47Vi2xTJT6I", "type": "rectangle", "x": 6988.465067243243, "y": 1342.5805123569526, "width": 204.71253913401046, "height": 78.72014580735438, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0R", "roundness": {"type": 3}, "seed": 927238350, "version": 591, "versionNonce": 2134710057, "isDeleted": false, "boundElements": [{"type": "text", "id": "TxvO-JruSGbPIpJnQJypT"}, {"id": "_TIoTwHIoWV2AeEkgVy4A", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "TxvO-JruSGbPIpJnQJypT", "type": "text", "x": 7032.941377703803, "y": 1369.4405852606299, "width": 115.75991821289062, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0S", "roundness": null, "seed": 798375694, "version": 541, "versionNonce": 599935497, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Select voice", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "VzBWXpj40g47Vi2xTJT6I", "originalText": "Select voice", "autoResize": true, "lineHeight": 1.25}, {"id": "whNK64cgSsrYU9K80MZZR", "type": "rectangle", "x": 6989.281388016921, "y": 1482.3437459696, "width": 204.71253913401046, "height": 78.72014580735438, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0T", "roundness": {"type": 3}, "seed": 1194459666, "version": 749, "versionNonce": 1377573833, "isDeleted": false, "boundElements": [{"type": "text", "id": "lQ_xYGdaOoWPsEeB-PlWC"}, {"id": "FeEgojYbfHaqDdwUJXZxl", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "lQ_xYGdaOoWPsEeB-PlWC", "type": "text", "x": 7018.577721060488, "y": 1509.2038188732772, "width": 146.119873046875, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0U", "roundness": null, "seed": 428601298, "version": 730, "versionNonce": 429374121, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Press generate", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "whNK64cgSsrYU9K80MZZR", "originalText": "Press generate", "autoResize": true, "lineHeight": 1.25}, {"id": "S2dXugYkirJEuDQNOcS64", "type": "rectangle", "x": 7339.703436897137, "y": 1483.7171252917383, "width": 204.71253913401046, "height": 78.72014580735438, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0V", "roundness": {"type": 3}, "seed": 476191762, "version": 2398, "versionNonce": 1912372617, "isDeleted": false, "boundElements": [{"type": "text", "id": "Q58vFBJKeO4HbSvFcgI8Z"}, {"id": "FeEgojYbfHaqDdwUJXZxl", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "Q58vFBJKeO4HbSvFcgI8Z", "type": "text", "x": 7377.609755292267, "y": 1498.0771981954156, "width": 128.89990234375, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0W", "roundness": null, "seed": 1168689618, "version": 2415, "versionNonce": 713417833, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "EC2 instance\nPort 8000", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "S2dXugYkirJEuDQNOcS64", "originalText": "EC2 instance\nPort 8000", "autoResize": true, "lineHeight": 1.25}, {"id": "1_8HYnBB_F1M_oxVq7IvA", "type": "rectangle", "x": 7341.057321841728, "y": 1622.0943551841533, "width": 204.71253913401046, "height": 78.72014580735438, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0X", "roundness": {"type": 3}, "seed": 1370616594, "version": 2495, "versionNonce": 520056649, "isDeleted": false, "boundElements": [{"type": "text", "id": "o_0QKQBF9CSIq7CwUBmTO"}, {"id": "I2ORBd6hNA4YOB3wcXLd_", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "o_0QKQBF9CSIq7CwUBmTO", "type": "text", "x": 7366.833650612834, "y": 1636.4544280878306, "width": 153.15988159179688, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0Y", "roundness": null, "seed": 283492562, "version": 2525, "versionNonce": 633642537, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Text-to-speech\nStyleTTS2", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "1_8HYnBB_F1M_oxVq7IvA", "originalText": "Text-to-speech\nStyleTTS2", "autoResize": true, "lineHeight": 1.25}, {"id": "nfh0aCh1H36DJY0oIqhza", "type": "rectangle", "x": 7341.789167741372, "y": 1761.4492512954205, "width": 204.71253913401046, "height": 78.72014580735438, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0Z", "roundness": {"type": 3}, "seed": 6675278, "version": 2610, "versionNonce": 361717255, "isDeleted": false, "boundElements": [{"id": "-bdu-15Q7ct9K1F1xgTc2", "type": "text"}, {"id": "n8ydTZusGbwoTC57bQ-5t", "type": "arrow"}, {"id": "QeQxW7D_SWA_f3HySkqDS", "type": "arrow"}], "updated": 1742298168016, "link": null, "locked": false}, {"id": "-bdu-15Q7ct9K1F1xgTc2", "type": "text", "x": 7350.505514212674, "y": 1775.8093241990978, "width": 187.27984619140625, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0ZG", "roundness": null, "seed": 107636178, "version": 341, "versionNonce": 2087708649, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Generated .wav file\nuploaded to S3", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "nfh0aCh1H36DJY0oIqhza", "originalText": "Generated .wav file uploaded to S3", "autoResize": true, "lineHeight": 1.25}, {"id": "VpOEX1Iz2QygI7zjq1kcO", "type": "rectangle", "x": 6995.849331722826, "y": 1759.9080491031257, "width": 204.71253913401046, "height": 78.72014580735438, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0b", "roundness": {"type": 3}, "seed": 1323741906, "version": 2742, "versionNonce": 1299983209, "isDeleted": false, "boundElements": [{"type": "text", "id": "F_NY9J04riVtlHBMunf8R"}, {"id": "yrJilUshi08yOKfuM7XxD", "type": "arrow"}, {"id": "QeQxW7D_SWA_f3HySkqDS", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "F_NY9J04riVtlHBMunf8R", "type": "text", "x": 7029.155651643835, "y": 1786.768122006803, "width": 138.0998992919922, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0c", "roundness": null, "seed": 1357678738, "version": 492, "versionNonce": 966055497, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Play audio file", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "VpOEX1Iz2QygI7zjq1kcO", "originalText": "Play audio file", "autoResize": true, "lineHeight": 1.25}, {"id": "_TIoTwHIoWV2AeEkgVy4A", "type": "arrow", "x": 7091.094173138826, "y": 1280.2766156873784, "width": 0.33158392341374565, "height": 61.71346291005807, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0d", "roundness": null, "seed": 629069262, "version": 471, "versionNonce": 477085511, "isDeleted": false, "boundElements": [], "updated": 1742298168016, "link": null, "locked": false, "points": [[0, 0], [-0.33158392341374565, 61.71346291005807]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "VzBWXpj40g47Vi2xTJT6I", "focus": -0.002665555610145078, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "FeEgojYbfHaqDdwUJXZxl", "type": "arrow", "x": 7322.630996229041, "y": 1521.6088040370264, "width": 3.0068203273240215, "height": 1.8825586040504731, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0f", "roundness": null, "seed": 2018429010, "version": 793, "versionNonce": 8378759, "isDeleted": false, "boundElements": [], "updated": 1742298168016, "link": null, "locked": false, "points": [[0, 0], [3.0068203273240215, 1.8825586040504731]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "I2ORBd6hNA4YOB3wcXLd_", "type": "arrow", "x": 7443.836841036537, "y": 1800.6906566098157, "width": 0, "height": 0.2373351785640807, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0h", "roundness": null, "seed": 537380114, "version": 877, "versionNonce": 950817511, "isDeleted": false, "boundElements": [], "updated": 1742298168016, "link": null, "locked": false, "points": [[0, 0], [0, 0.2373351785640807]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "VuqbJBQHsbOGJF2TAIFWA", "type": "text", "x": 7228.771056653844, "y": 1462.8434580610142, "width": 79.85993957519531, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0j", "roundness": null, "seed": 389116430, "version": 459, "versionNonce": 846794919, "isDeleted": false, "boundElements": [], "updated": 1742298168016, "link": null, "locked": false, "text": "HTTP\nRequest", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "HTTP\nRequest", "autoResize": true, "lineHeight": 1.25}, {"id": "AyDXw0tRkW2xY-Mic1P0z", "type": "text", "x": 7230.309561601594, "y": 1820.5166854442766, "width": 91.17991638183594, "height": 150, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0k", "roundness": null, "seed": 1401379794, "version": 734, "versionNonce": 1796422985, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "HTTP\nResponse\ncontains\nlink\nto\nS3 item", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "HTTP\nResponse\ncontains\nlink\nto\nS3 item", "autoResize": true, "lineHeight": 1.25}, {"id": "BR44Wv8_MGm-RBjx02A83", "type": "text", "x": 7043.9312573648895, "y": 1101.7142256853995, "width": 88.39993286132812, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0l", "roundness": null, "seed": 1036724046, "version": 212, "versionNonce": 439266345, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Frontend", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Frontend", "autoResize": true, "lineHeight": 1.25}, {"id": "nphVEEOAZnfSM-MbrWh6l", "type": "text", "x": 7397.0565393357, "y": 1100.8800680781455, "width": 80.83992004394531, "height": 25, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0m", "roundness": null, "seed": 196521106, "version": 416, "versionNonce": 1975074569, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Backend", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Backend", "autoResize": true, "lineHeight": 1.25}, {"id": "gdkSuw6uoSUZKI8Sy51zu", "type": "text", "x": 7178.927480922737, "y": 1012.2289019801954, "width": 153.15988159179688, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0n", "roundness": null, "seed": 1384379598, "version": 263, "versionNonce": 1814984169, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Text-to-speech", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Text-to-speech", "autoResize": true, "lineHeight": 1.25}, {"id": "lFnqW6u5L7w0LWBYlxo3a", "type": "rectangle", "x": 8849.68977689319, "y": 1191.6894069385428, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0o", "roundness": {"type": 3}, "seed": 660413134, "version": 737, "versionNonce": 278515913, "isDeleted": false, "boundElements": [{"type": "text", "id": "-mIidecYLbu0f2l3D-XBR"}, {"id": "eem8gRgTbehESDRHHxDIO", "type": "arrow"}, {"id": "BTOeb9fXX0bh84--<PERSON><PERSON><PERSON>", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "-mIidecYLbu0f2l3D-XBR", "type": "text", "x": 8889.6213111885, "y": 1218.5587236721838, "width": 124.919921875, "height": 25.00860369031483, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0p", "roundness": null, "seed": 1926009102, "version": 697, "versionNonce": 144101289, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Upload audio", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "lFnqW6u5L7w0LWBYlxo3a", "originalText": "Upload audio", "autoResize": true, "lineHeight": 1.25}, {"id": "mWC0EcNuDxujdNS3pS0g6", "type": "rectangle", "x": 8850.791318102505, "y": 1332.5693407612225, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0q", "roundness": {"type": 3}, "seed": 91438926, "version": 909, "versionNonce": 978383497, "isDeleted": false, "boundElements": [{"type": "text", "id": "0C8RV31JS6_mf7MIfPTqx"}, {"id": "7_z2Ydn-z-lg7IZV-3Smd", "type": "arrow"}, {"id": "eem8gRgTbehESDRHHxDIO", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "0C8RV31JS6_mf7MIfPTqx", "type": "text", "x": 8895.30285422887, "y": 1359.4386574948635, "width": 115.75991821289062, "height": 25.00860369031483, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0r", "roundness": null, "seed": 1672095118, "version": 861, "versionNonce": 981711209, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Select voice", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "mWC0EcNuDxujdNS3pS0g6", "originalText": "Select voice", "autoResize": true, "lineHeight": 1.25}, {"id": "YjL9yLUzK_S5HZz5HaNOt", "type": "rectangle", "x": 8851.6855103783, "y": 1472.3030829897737, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0s", "roundness": {"type": 3}, "seed": 514474958, "version": 1063, "versionNonce": 907096873, "isDeleted": false, "boundElements": [{"type": "text", "id": "5hHhXLE40xkgqSOLrdj_p"}, {"id": "wsXmU6legvGqsKZ50YylB", "type": "arrow"}, {"id": "7_z2Ydn-z-lg7IZV-3Smd", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "5hHhXLE40xkgqSOLrdj_p", "type": "text", "x": 8881.017069087673, "y": 1499.1723997234146, "width": 146.119873046875, "height": 25.00860369031483, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0t", "roundness": null, "seed": 948738574, "version": 1047, "versionNonce": 565372425, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Press generate", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "YjL9yLUzK_S5HZz5HaNOt", "originalText": "Press generate", "autoResize": true, "lineHeight": 1.25}, {"id": "FCX1JMmR8SkpVgZkOHt9u", "type": "rectangle", "x": 9202.150565602768, "y": 1473.7545255243992, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0u", "roundness": {"type": 3}, "seed": 139077710, "version": 2714, "versionNonce": 160806857, "isDeleted": false, "boundElements": [{"type": "text", "id": "KEiRdjfYMngLAKlK65PBY"}, {"id": "wsXmU6legvGqsKZ50YylB", "type": "arrow"}, {"id": "kL3qQXamiso41-QFD7WLF", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "KEiRdjfYMngLAKlK65PBY", "type": "text", "x": 9240.092109663703, "y": 1488.119540412883, "width": 128.89990234375, "height": 50.01720738062966, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0v", "roundness": null, "seed": 498855566, "version": 2737, "versionNonce": 1736300201, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "EC2 instance\nPort 8001", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "FCX1JMmR8SkpVgZkOHt9u", "originalText": "EC2 instance\nPort 8001", "autoResize": true, "lineHeight": 1.25}, {"id": "X6dFm7Fq9ea7GRElPQoCs", "type": "rectangle", "x": 9203.414098166393, "y": 1612.179377610119, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0w", "roundness": {"type": 3}, "seed": 630910158, "version": 2814, "versionNonce": 1421810057, "isDeleted": false, "boundElements": [{"type": "text", "id": "FvDxgE_0gfKKcUwp8IfxB"}, {"id": "q8_lMr91AmHXScFsgD0ue", "type": "arrow"}, {"id": "8xZHEisgu66hHXhLypihE", "type": "arrow"}, {"id": "kL3qQXamiso41-QFD7WLF", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "FvDxgE_0gfKKcUwp8IfxB", "type": "text", "x": 9233.745641616977, "y": 1626.5443924986027, "width": 144.11990356445312, "height": 50.01720738062966, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0x", "roundness": null, "seed": 1300243214, "version": 2845, "versionNonce": 1385496681, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Downloads\nuploaded audio", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "X6dFm7Fq9ea7GRElPQoCs", "originalText": "Downloads uploaded audio", "autoResize": true, "lineHeight": 1.25}, {"id": "yfAZeRbVuFUdI0gIuIXhq", "type": "rectangle", "x": 9209.504067439391, "y": 1900.5610730244878, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0y", "roundness": {"type": 3}, "seed": 669290830, "version": 3073, "versionNonce": 1908193833, "isDeleted": false, "boundElements": [{"type": "text", "id": "RLSP3BXH4PBjRjAZStq-7"}, {"id": "-_YNElK90gZwYtxyrv1-w", "type": "arrow"}, {"id": "pXZzjO0iZmdBydcxRoj96", "type": "arrow"}, {"id": "Gzj6OB2-ezd3swrdaSFe-", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "RLSP3BXH4PBjRjAZStq-7", "type": "text", "x": 9220.995637440268, "y": 1914.9260879129715, "width": 181.7998504638672, "height": 50.01720738062966, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b0z", "roundness": null, "seed": 270612366, "version": 825, "versionNonce": 206933257, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Generated wav file\nuploaded to S3", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "yfAZeRbVuFUdI0gIuIXhq", "originalText": "Generated wav file uploaded to S3", "autoResize": true, "lineHeight": 1.25}, {"id": "ELPBrzK1PE5j3-SlY2WXj", "type": "rectangle", "x": 8863.261923798547, "y": 1898.6588884783564, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b10", "roundness": {"type": 3}, "seed": 242161102, "version": 3204, "versionNonce": 967695785, "isDeleted": false, "boundElements": [{"type": "text", "id": "CEWfBNvRvnldg6JbQQyp2"}, {"id": "-_YNElK90gZwYtxyrv1-w", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "CEWfBNvRvnldg6JbQQyp2", "type": "text", "x": 8896.603469385362, "y": 1925.5282052119974, "width": 138.0998992919922, "height": 25.00860369031483, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b11", "roundness": null, "seed": 1005005838, "version": 965, "versionNonce": 290865289, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Play audio file", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "ELPBrzK1PE5j3-SlY2WXj", "originalText": "Play audio file", "autoResize": true, "lineHeight": 1.25}, {"id": "eem8gRgTbehESDRHHxDIO", "type": "arrow", "x": 8953.396641923007, "y": 1270.3031039912128, "width": 0.25980779354540573, "height": 61.69769903009865, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b12", "roundness": null, "seed": 646085198, "version": 1406, "versionNonce": 1948517671, "isDeleted": false, "boundElements": [], "updated": 1742298168016, "link": null, "locked": false, "points": [[0, 0], [-0.25980779354540573, 61.69769903009865]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "mWC0EcNuDxujdNS3pS0g6", "focus": -0.002088343247687458, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "7_z2Ydn-z-lg7IZV-3Smd", "type": "arrow", "x": 8957.219637884742, "y": 1411.579003912803, "width": 0.3663753293967602, "height": 60.36033551791343, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b13", "roundness": null, "seed": 890896526, "version": 1399, "versionNonce": 1675020359, "isDeleted": false, "boundElements": [], "updated": 1742298168016, "link": null, "locked": false, "points": [[0, 0], [-0.3663753293967602, 60.36033551791343]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "YjL9yLUzK_S5HZz5HaNOt", "focus": 0.024700838029048783, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "wsXmU6legvGqsKZ50YylB", "type": "arrow", "x": 9185.067431428397, "y": 1513.54721202588, "width": 107.88742318274126, "height": 0.08011437828872658, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b14", "roundness": null, "seed": 191282894, "version": 1666, "versionNonce": 1048093319, "isDeleted": false, "boundElements": [], "updated": 1742298168016, "link": null, "locked": false, "points": [[0, 0], [-107.88742318274126, -0.08011437828872658]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "kL3qQXamiso41-QFD7WLF", "type": "arrow", "x": 9308.637202272557, "y": 1557.5461734552362, "width": 3.3076467177306768, "height": 54.26946059582565, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b15", "roundness": null, "seed": 1845605646, "version": 1801, "versionNonce": 1167649991, "isDeleted": false, "boundElements": [], "updated": 1742298168017, "link": null, "locked": false, "points": [[0, 0], [-3.3076467177306768, 54.26946059582565]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "X6dFm7Fq9ea7GRElPQoCs", "focus": -0.027654727249713136, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "-_YNElK90gZwYtxyrv1-w", "type": "arrow", "x": 9209.39487547782, "y": 1937.6380105746653, "width": 140.34961706603826, "height": 0.65735408196565, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b17", "roundness": null, "seed": 1726368142, "version": 2142, "versionNonce": 1905114087, "isDeleted": false, "boundElements": [], "updated": 1742298168017, "link": null, "locked": false, "points": [[0, 0], [-140.34961706603826, 0.65735408196565]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "myL7JHVrSPu608rWRh8r8", "type": "text", "x": 9091.180008245656, "y": 1452.7960841036595, "width": 79.88742318274195, "height": 50.01720738062966, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b18", "roundness": null, "seed": 784459726, "version": 772, "versionNonce": 678476199, "isDeleted": false, "boundElements": [], "updated": 1742298168016, "link": null, "locked": false, "text": "HTTP\nRequest", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "HTTP\nRequest", "autoResize": true, "lineHeight": 1.25}, {"id": "kS50wiWeGntLHJgFqQovN", "type": "text", "x": 9097.069829591148, "y": 1967.8222977444077, "width": 91.21129573237519, "height": 150.05162214188897, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b19", "roundness": null, "seed": 772783630, "version": 1172, "versionNonce": 1616619271, "isDeleted": false, "boundElements": [], "updated": 1742298168017, "link": null, "locked": false, "text": "HTTP\nResponse\ncontains\nlink\nto\naudio file", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "HTTP\nResponse\ncontains\nlink\nto\naudio file", "autoResize": true, "lineHeight": 1.25}, {"id": "pxwu9DnHjADQZz3cTh8ZI", "type": "text", "x": 8906.276596781065, "y": 1091.6201605321573, "width": 88.43035548717575, "height": 25.00860369031483, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1A", "roundness": null, "seed": 701025358, "version": 528, "versionNonce": 572971337, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Frontend", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Frontend", "autoResize": true, "lineHeight": 1.25}, {"id": "aMxSUcyBqRaf0nmO14F5t", "type": "text", "x": 9259.523405974609, "y": 1090.7857158515542, "width": 80.86774090943065, "height": 25.00860369031483, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1B", "roundness": null, "seed": 1606816398, "version": 732, "versionNonce": 1684981801, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Backend", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Backend", "autoResize": true, "lineHeight": 1.25}, {"id": "9YPB4d7drP60kbpAQTErG", "type": "text", "x": 9041.319278966954, "y": 1002.1040406664376, "width": 157.27987670898438, "height": 25.00860369031483, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1C", "roundness": null, "seed": 1791004878, "version": 596, "versionNonce": 966122249, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Voice conversion", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Voice conversion", "autoResize": true, "lineHeight": 1.25}, {"id": "EhQvnLExjFHhoBs-JONpg", "type": "diamond", "x": 7108.263381365199, "y": 2056.863552689595, "width": 315.35305355544006, "height": 186.4982349248785, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1D", "roundness": {"type": 2}, "seed": 1529216526, "version": 284, "versionNonce": 1108840937, "isDeleted": false, "boundElements": [{"type": "text", "id": "9Xx6YNOvSoPp1BOw2ht8_"}, {"id": "n8ydTZusGbwoTC57bQ-5t", "type": "arrow"}, {"id": "yrJilUshi08yOKfuM7XxD", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "9Xx6YNOvSoPp1BOw2ht8_", "type": "text", "x": 7217.50168442691, "y": 2137.4881114208147, "width": 97.19992065429688, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1E", "roundness": null, "seed": 1751097810, "version": 172, "versionNonce": 50293961, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "S3 bucket", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "EhQvnLExjFHhoBs-JONpg", "originalText": "S3 bucket", "autoResize": true, "lineHeight": 1.25}, {"id": "n8ydTZusGbwoTC57bQ-5t", "type": "arrow", "x": 7445.794791999946, "y": 1841.2632722381684, "width": 104.4099666672164, "height": 259.64838224977393, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1F", "roundness": null, "seed": 1294313298, "version": 425, "versionNonce": 2095370567, "isDeleted": false, "boundElements": [], "updated": 1742298168017, "link": null, "locked": false, "points": [[0, 0], [-104.4099666672164, 259.64838224977393]], "lastCommittedPoint": null, "startBinding": {"elementId": "nfh0aCh1H36DJY0oIqhza", "focus": -0.15160052563964665, "gap": 1.0938751353935459}, "endBinding": {"elementId": "EhQvnLExjFHhoBs-JONpg", "focus": 0.5968976020283301, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "gdASm4c9teLnXBkkwIAyZ", "type": "text", "x": 7420.9564747688155, "y": 1965.5492004816213, "width": 129.11993408203125, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1H", "roundness": null, "seed": 402856338, "version": 144, "versionNonce": 1865564233, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Upload to S3", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Upload to S3", "autoResize": true, "lineHeight": 1.25}, {"id": "yrJilUshi08yOKfuM7XxD", "type": "arrow", "x": 7100.380496384842, "y": 1838.9919401093127, "width": 93.15347602685506, "height": 257.2624694766614, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1I", "roundness": null, "seed": 1525795346, "version": 393, "versionNonce": 1347971975, "isDeleted": false, "boundElements": [], "updated": 1742298168017, "link": null, "locked": false, "points": [[0, 0], [93.15347602685506, 257.2624694766614]], "lastCommittedPoint": null, "startBinding": {"elementId": "VpOEX1Iz2QygI7zjq1kcO", "focus": 0.10470007803255257, "gap": 1}, "endBinding": {"elementId": "EhQvnLExjFHhoBs-JONpg", "focus": -0.5673418223766592, "gap": 3.333241817250876}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "MI5VOLmQKdjy_KlCtz4J7", "type": "text", "x": 7021.727105726251, "y": 1943.749568208524, "width": 102.87992858886719, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1J", "roundness": null, "seed": 1214297874, "version": 172, "versionNonce": 171147785, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Plays from\nS3", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Plays from\nS3", "autoResize": true, "lineHeight": 1.25}, {"id": "D_CgUPzhkeE9NpluYKo-Q", "type": "diamond", "x": 8977.992396334657, "y": 2150.6013385649394, "width": 315.35305355544006, "height": 186.4982349248785, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1K", "roundness": {"type": 2}, "seed": 750530702, "version": 720, "versionNonce": 510154985, "isDeleted": false, "boundElements": [{"type": "text", "id": "ZvbPrw2GR7QJS8fsJL4P7"}, {"id": "BTOeb9fXX0bh84--<PERSON><PERSON><PERSON>", "type": "arrow"}, {"id": "q8_lMr91AmHXScFsgD0ue", "type": "arrow"}, {"id": "Gzj6OB2-ezd3swrdaSFe-", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "ZvbPrw2GR7QJS8fsJL4P7", "type": "text", "x": 9087.230699396368, "y": 2231.225897296159, "width": 97.19992065429688, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1L", "roundness": null, "seed": 792915662, "version": 606, "versionNonce": 1201624009, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "S3 bucket", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "D_CgUPzhkeE9NpluYKo-Q", "originalText": "S3 bucket", "autoResize": true, "lineHeight": 1.25}, {"id": "BTOeb9fXX0bh84--<PERSON><PERSON><PERSON>", "type": "arrow", "x": 8849.133299231451, "y": 1232.1445028631174, "width": 234.95774460119446, "height": 1007.9181460990528, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1M", "roundness": {"type": 2}, "seed": 88296082, "version": 1366, "versionNonce": 1567580839, "isDeleted": false, "boundElements": [], "updated": 1742298168017, "link": null, "locked": false, "points": [[0, 0], [-101.65826460351036, 448.5330281476997], [133.2994799976841, 1007.9181460990528]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "D_CgUPzhkeE9NpluYKo-Q", "focus": -1.6262369096239249, "gap": 6.338452270618979}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "YVunMUoFzP3u1mDzNl2hz", "type": "text", "x": 8651.142937170314, "y": 1620.1566661500424, "width": 76.3199462890625, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1N", "roundness": null, "seed": 1391437330, "version": 464, "versionNonce": 709154345, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Uploads\nto S3", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Uploads\nto S3", "autoResize": true, "lineHeight": 1.25}, {"id": "q8_lMr91AmHXScFsgD0ue", "type": "arrow", "x": 9409.55721720966, "y": 1652.3915046034324, "width": 207.80463837456318, "height": 591.4589514239465, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1O", "roundness": {"type": 2}, "seed": 1453971730, "version": 1169, "versionNonce": 383415751, "isDeleted": false, "boundElements": [], "updated": 1742298168018, "link": null, "locked": false, "points": [[0, 0], [82.31617925242153, 213.6200911773608], [-125.48845912214165, 591.4589514239465]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "D_CgUPzhkeE9NpluYKo-Q", "focus": 1.5914342034916704, "gap": 1}, "startArrowhead": "arrow", "endArrowhead": "arrow", "elbowed": false}, {"id": "ZS4keImhTQwVzvgRvWM69", "type": "rectangle", "x": 9206.015837367284, "y": 1750.3987122958208, "width": 204.7829904656213, "height": 86, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1Q", "roundness": {"type": 3}, "seed": 1961264466, "version": 3119, "versionNonce": 406986729, "isDeleted": false, "boundElements": [{"type": "text", "id": "hoDksotDAKP4jH2tY8Z2j"}, {"id": "8xZHEisgu66hHXhLypihE", "type": "arrow"}, {"id": "pXZzjO0iZmdBydcxRoj96", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "hoDksotDAKP4jH2tY8Z2j", "type": "text", "x": 9229.767394245602, "y": 1768.390108605506, "width": 157.27987670898438, "height": 50.01720738062966, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1R", "roundness": null, "seed": 2085489426, "version": 922, "versionNonce": 279460553, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Voice conversion\nseed-vc", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "ZS4keImhTQwVzvgRvWM69", "originalText": "Voice conversion\nseed-vc", "autoResize": true, "lineHeight": 1.25}, {"id": "8xZHEisgu66hHXhLypihE", "type": "arrow", "x": 9309.778776304696, "y": 1690.9810929811563, "width": 1.5710505740462395, "height": 58.332112773319295, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1S", "roundness": {"type": 2}, "seed": 946467982, "version": 848, "versionNonce": 330704103, "isDeleted": false, "boundElements": [], "updated": 1742298168018, "link": null, "locked": false, "points": [[0, 0], [-1.5710505740462395, 58.332112773319295]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "ZS4keImhTQwVzvgRvWM69", "focus": -0.013394117367711164, "gap": 1.0855065413452394}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "pXZzjO0iZmdBydcxRoj96", "type": "arrow", "x": 9312.207440916935, "y": 1836.7902947298458, "width": 0.10978468137363961, "height": 63.40703473558483, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1T", "roundness": {"type": 2}, "seed": 823869778, "version": 931, "versionNonce": 811685671, "isDeleted": false, "boundElements": [], "updated": 1742298168018, "link": null, "locked": false, "points": [[0, 0], [0.10978468137363961, 63.40703473558483]], "lastCommittedPoint": null, "startBinding": {"elementId": "ZS4keImhTQwVzvgRvWM69", "focus": -0.036353335546050046, "gap": 1}, "endBinding": {"elementId": "yfAZeRbVuFUdI0gIuIXhq", "focus": 0.004786910760269131, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "Gzj6OB2-ezd3swrdaSFe-", "type": "arrow", "x": 9313.524584430581, "y": 1979.672053741142, "width": 107.21905392887857, "height": 212.0684643739189, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b1U", "roundness": null, "seed": 285396242, "version": 972, "versionNonce": 1494546791, "isDeleted": false, "boundElements": [], "updated": 1742298168018, "link": null, "locked": false, "points": [[0, 0], [-107.21905392887857, 212.0684643739189]], "lastCommittedPoint": null, "startBinding": {"elementId": "yfAZeRbVuFUdI0gIuIXhq", "focus": -0.1775962332247824, "gap": 1}, "endBinding": {"elementId": "D_CgUPzhkeE9NpluYKo-Q", "focus": 0.4749696574836085, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "YWb8TxNtwgamnFTWp7ar3", "type": "rectangle", "x": 7842.233728089365, "y": 1199.9755193067278, "width": 204.71253913401046, "height": 78.72014580735438, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b23", "roundness": {"type": 3}, "seed": 879066894, "version": 879, "versionNonce": 1114230793, "isDeleted": false, "boundElements": [{"type": "text", "id": "82Wy0YoH7Imt3qfcAu9Th"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "82Wy0YoH7Imt3qfcAu9Th", "type": "text", "x": 7890.900025732542, "y": 1226.8355922104051, "width": 107.37994384765625, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b24", "roundness": null, "seed": 1102998862, "version": 820, "versionNonce": 1453647593, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Enter text", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "YWb8TxNtwgamnFTWp7ar3", "originalText": "Enter text", "autoResize": true, "lineHeight": 1.25}, {"id": "eusvosF2KuUMuZGfafRZP", "type": "rectangle", "x": 7833.358994923502, "y": 1344.361294816501, "width": 204.71253913401046, "height": 78.72014580735438, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b27", "roundness": {"type": 3}, "seed": 121538574, "version": 1239, "versionNonce": 622469577, "isDeleted": false, "boundElements": [{"type": "text", "id": "tDNckjQxNk_04oo_gE0G5"}, {"id": "4918Nnizfh-_WSBGXDBpa", "type": "arrow"}, {"id": "Gv9CIfLc25NhsztxdnXxx", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "tDNckjQxNk_04oo_gE0G5", "type": "text", "x": 7862.65532796707, "y": 1371.2213677201783, "width": 146.119873046875, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b28", "roundness": null, "seed": **********, "version": 1221, "versionNonce": 719650985, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Press generate", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "eusvosF2KuUMuZGfafRZP", "originalText": "Press generate", "autoResize": true, "lineHeight": 1.25}, {"id": "lf_PCLQ_NOR1gD_Ojxdw-", "type": "rectangle", "x": 8183.703479929869, "y": 1345.8122380124896, "width": 204.71253913401046, "height": 78.72014580735438, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b29", "roundness": {"type": 3}, "seed": 524967054, "version": 2890, "versionNonce": 1371122281, "isDeleted": false, "boundElements": [{"type": "text", "id": "Qoxqo4MWa0OIWjeGhpyL0"}, {"id": "4918Nnizfh-_WSBGXDBpa", "type": "arrow"}, {"id": "aF8PcD6uIyHgqx3BVmDhO", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "Qoxqo4MWa0OIWjeGhpyL0", "type": "text", "x": 8221.609798324998, "y": 1360.172310916167, "width": 128.89990234375, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2A", "roundness": null, "seed": 120298190, "version": 2912, "versionNonce": 2009757001, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "EC2 instance\nPort 8002", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "lf_PCLQ_NOR1gD_Ojxdw-", "originalText": "EC2 instance\nPort 8002", "autoResize": true, "lineHeight": 1.25}, {"id": "4lizRKQ0rbKvWCOf_zW-3", "type": "rectangle", "x": 8185.3613794204375, "y": 1484.1894679049046, "width": 204.71253913401046, "height": 78.72014580735438, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2B", "roundness": {"type": 3}, "seed": 1422594318, "version": 2989, "versionNonce": 2067719209, "isDeleted": false, "boundElements": [{"type": "text", "id": "wVZc4yOlbVDfFDwJiH7Ix"}, {"id": "04XT2qHHGBdAtdgEcpJHz", "type": "arrow"}, {"id": "aF8PcD6uIyHgqx3BVmDhO", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "wVZc4yOlbVDfFDwJiH7Ix", "type": "text", "x": 8218.627713684707, "y": 1498.549540808582, "width": 138.17987060546875, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2C", "roundness": null, "seed": 597802830, "version": 3043, "versionNonce": 939685641, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Text-to-SFX\nMake-an-audio", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "4lizRKQ0rbKvWCOf_zW-3", "originalText": "Text-to-SFX\nMake-an-audio", "autoResize": true, "lineHeight": 1.25}, {"id": "NbopGwGSSZPzknGMMMOKI", "type": "rectangle", "x": 8185.7074319472285, "y": 1623.4590883060998, "width": 204.71253913401046, "height": 78.72014580735438, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2D", "roundness": {"type": 3}, "seed": 940090766, "version": 3094, "versionNonce": 1274349769, "isDeleted": false, "boundElements": [{"type": "text", "id": "q-acO-qoR743QTdsvnMdG"}, {"id": "V0vEpjNL2c3tkKI3X_2Pu", "type": "arrow"}, {"id": "04XT2qHHGBdAtdgEcpJHz", "type": "arrow"}, {"id": "5QciYpYUOQ94kkUGgLhli", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "q-acO-qoR743QTdsvnMdG", "type": "text", "x": 8197.1637762823, "y": 1637.8191612097771, "width": 181.7998504638672, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2E", "roundness": null, "seed": 1664664526, "version": 827, "versionNonce": 600508329, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Generated wav file\nuploaded to S3", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "NbopGwGSSZPzknGMMMOKI", "originalText": "Generated wav file uploaded to S3", "autoResize": true, "lineHeight": 1.25}, {"id": "Z2fkFmqJ9XSubnSeo4VNm", "type": "rectangle", "x": 7839.454573136493, "y": 1621.767554406049, "width": 204.71253913401046, "height": 78.72014580735438, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2F", "roundness": {"type": 3}, "seed": 1732975118, "version": 3253, "versionNonce": 1920588873, "isDeleted": false, "boundElements": [{"type": "text", "id": "ERHHoW9sM7r7FW7mro7Ci"}, {"id": "hhFKJR7imk9-h8pYP3uM0", "type": "arrow"}, {"id": "V0vEpjNL2c3tkKI3X_2Pu", "type": "arrow"}], "updated": 1742298167952, "link": null, "locked": false}, {"id": "ERHHoW9sM7r7FW7mro7Ci", "type": "text", "x": 7872.7608930575025, "y": 1648.6276273097262, "width": 138.0998992919922, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2G", "roundness": null, "seed": **********, "version": 1005, "versionNonce": **********, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Play audio file", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "Z2fkFmqJ9XSubnSeo4VNm", "originalText": "Play audio file", "autoResize": true, "lineHeight": 1.25}, {"id": "Gv9CIfLc25NhsztxdnXxx", "type": "arrow", "x": 7938.85681560682, "y": 1283.658106596833, "width": 0.366248516061205, "height": 60.339443020835006, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2I", "roundness": null, "seed": **********, "version": 1582, "versionNonce": 522138759, "isDeleted": false, "boundElements": [], "updated": 1742298168019, "link": null, "locked": false, "points": [[0, 0], [-0.366248516061205, 60.339443020835006]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "eusvosF2KuUMuZGfafRZP", "focus": 0.024700838029042542, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "4918Nnizfh-_WSBGXDBpa", "type": "arrow", "x": 8166.63103926177, "y": 1385.5912382437798, "width": 12.464514953073376, "height": 0.009255822751811138, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2J", "roundness": null, "seed": 1761518350, "version": 1941, "versionNonce": 443878311, "isDeleted": false, "boundElements": [], "updated": 1742298168019, "link": null, "locked": false, "points": [[0, 0], [12.464514953073376, 0.009255822751811138]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "aF8PcD6uIyHgqx3BVmDhO", "type": "arrow", "x": 8290.153482085554, "y": 1429.5750591684018, "width": 0.23058644152479246, "height": 54.250663537670334, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2K", "roundness": null, "seed": 1335038286, "version": 2333, "versionNonce": 673549799, "isDeleted": false, "boundElements": [], "updated": 1742298168019, "link": null, "locked": false, "points": [[0, 0], [0.23058644152479246, 54.250663537670334]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "4lizRKQ0rbKvWCOf_zW-3", "focus": 0.02765472724974044, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "04XT2qHHGBdAtdgEcpJHz", "type": "arrow", "x": 8291.056441140407, "y": 1563.909613712259, "width": 2.0594208315560536, "height": 59.18572939500791, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2L", "roundness": null, "seed": 1040168846, "version": 2340, "versionNonce": 1479096583, "isDeleted": false, "boundElements": [], "updated": 1742298168019, "link": null, "locked": false, "points": [[0, 0], [-2.0594208315560536, 59.18572939500791]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "NbopGwGSSZPzknGMMMOKI", "focus": -0.004327825031652099, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "PbU3IJBf_xGiZQmHAlcOJ", "type": "text", "x": 8072.771099686574, "y": 1324.8610069079155, "width": 79.85993957519531, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2N", "roundness": null, "seed": 1680190478, "version": 950, "versionNonce": 1464008391, "isDeleted": false, "boundElements": [], "updated": 1742298168019, "link": null, "locked": false, "text": "HTTP\nRequest", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "HTTP\nRequest", "autoResize": true, "lineHeight": 1.25}, {"id": "l67Hl1gFzgfX-e1x4U8ZU", "type": "text", "x": 8074.309604634326, "y": 1682.6117981650277, "width": 91.17991638183594, "height": 150, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2O", "roundness": null, "seed": 1123088974, "version": 1229, "versionNonce": 937162471, "isDeleted": false, "boundElements": [], "updated": 1742298168020, "link": null, "locked": false, "text": "HTTP\nResponse\ncontains\nlink\nto\nS3 item", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "HTTP\nResponse\ncontains\nlink\nto\nS3 item", "autoResize": true, "lineHeight": 1.25}, {"id": "CF-_hZSAH4LhmhkYpCOvr", "type": "text", "x": 7898.801080457967, "y": 1100.2069300245785, "width": 88.39993286132812, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2P", "roundness": null, "seed": 1069056142, "version": 670, "versionNonce": 1827002057, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Frontend", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Frontend", "autoResize": true, "lineHeight": 1.25}, {"id": "kJUMAKlz749js7hwNa-LS", "type": "text", "x": 8251.92636242878, "y": 1099.3727724173245, "width": 80.83992004394531, "height": 25, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2Q", "roundness": null, "seed": 1043676878, "version": 874, "versionNonce": 1232213417, "isDeleted": false, "boundElements": [], "updated": 1742298167952, "link": null, "locked": false, "text": "Backend", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Backend", "autoResize": true, "lineHeight": 1.25}, {"id": "POy7pbW9P3WbV7F_XBNSh", "type": "text", "x": 8033.797304015817, "y": 1010.7216063193744, "width": 126.87991333007812, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2R", "roundness": null, "seed": 1124087054, "version": 724, "versionNonce": 198408329, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Text-to-SFX", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Text-to-SFX", "autoResize": true, "lineHeight": 1.25}, {"id": "syXiBtk9KJ6Efb4c1Th6h", "type": "diamond", "x": 7952.263424397929, "y": 1918.9586654103466, "width": 315.35305355544006, "height": 186.4982349248785, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2S", "roundness": {"type": 2}, "seed": 1469211470, "version": 774, "versionNonce": 96829289, "isDeleted": false, "boundElements": [{"type": "text", "id": "NzhZlA6Z-Lm5llPd1Vpui"}, {"id": "5QciYpYUOQ94kkUGgLhli", "type": "arrow"}, {"id": "hhFKJR7imk9-h8pYP3uM0", "type": "arrow"}], "updated": 1742298167953, "link": null, "locked": false}, {"id": "NzhZlA6Z-Lm5llPd1Vpui", "type": "text", "x": 8061.501727459641, "y": 1999.5832241415662, "width": 97.19992065429688, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2T", "roundness": null, "seed": 1850001806, "version": 664, "versionNonce": 1167305289, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "S3 bucket", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "syXiBtk9KJ6Efb4c1Th6h", "originalText": "S3 bucket", "autoResize": true, "lineHeight": 1.25}, {"id": "5QciYpYUOQ94kkUGgLhli", "type": "arrow", "x": 8289.724748182238, "y": 1703.2731092488473, "width": 104.35301112238358, "height": 259.7257138072009, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2U", "roundness": null, "seed": 1179674574, "version": 1902, "versionNonce": 1207296839, "isDeleted": false, "boundElements": [], "updated": 1742298168019, "link": null, "locked": false, "points": [[0, 0], [-104.35301112238358, 259.7257138072009]], "lastCommittedPoint": null, "startBinding": {"elementId": "NbopGwGSSZPzknGMMMOKI", "focus": -0.15160052751003136, "gap": 1.0938751353930911}, "endBinding": {"elementId": "syXiBtk9KJ6Efb4c1Th6h", "focus": 0.5969014304646117, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "FNKHItMhdEwfir2FLPd5_", "type": "text", "x": 8264.956517801545, "y": 1827.6443132023724, "width": 129.11993408203125, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2V", "roundness": null, "seed": 1338916366, "version": 636, "versionNonce": 1962655177, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Upload to S3", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Upload to S3", "autoResize": true, "lineHeight": 1.25}, {"id": "hhFKJR7imk9-h8pYP3uM0", "type": "arrow", "x": 7943.817418718613, "y": 1700.8514454122362, "width": 94.74744312573694, "height": 258.27016352886335, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2W", "roundness": null, "seed": 263670862, "version": 1939, "versionNonce": 332201351, "isDeleted": false, "boundElements": [], "updated": 1742298168020, "link": null, "locked": false, "points": [[0, 0], [94.74744312573694, 258.27016352886335]], "lastCommittedPoint": null, "startBinding": {"elementId": "Z2fkFmqJ9XSubnSeo4VNm", "focus": 0.10759187951010388, "gap": 1}, "endBinding": {"elementId": "syXiBtk9KJ6Efb4c1Th6h", "focus": -0.5565759267018306, "gap": 2.142641256640937}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "g-gu0SMavGk1Hha9qYrPs", "type": "text", "x": 7865.7271487589815, "y": 1805.8446809292755, "width": 102.87992858886719, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2X", "roundness": null, "seed": 65311374, "version": 664, "versionNonce": 920362889, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Plays from\nS3", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Plays from\nS3", "autoResize": true, "lineHeight": 1.25}, {"id": "V0vEpjNL2c3tkKI3X_2Pu", "type": "arrow", "x": 8053.484587784602, "y": 1662.4645641967961, "width": 8.38597702761217, "height": 0.007976697865160531, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2Y", "roundness": null, "seed": 703586894, "version": 1247, "versionNonce": 802326471, "isDeleted": false, "boundElements": [], "updated": 1742298168020, "link": null, "locked": false, "points": [[0, 0], [-8.38597702761217, -0.007976697865160531]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "Z2fkFmqJ9XSubnSeo4VNm", "focus": 0.031190913492229987, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "cvTn3QSEfZxEfPeRaeITg", "type": "line", "x": 7675.139433641011, "y": 924.4541691702127, "width": 0, "height": 1265.2202320419033, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2Z", "roundness": {"type": 2}, "seed": 1770600334, "version": 176, "versionNonce": 738070857, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "points": [[0, 0], [0, 1265.2202320419033]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "_alWOy7HXOHtqSOl8g-No", "type": "line", "x": 8571.754233600192, "y": 929.6163161048212, "width": 0, "height": 1265.2202320419033, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2a", "roundness": {"type": 2}, "seed": 2136288078, "version": 297, "versionNonce": 513736745, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "points": [[0, 0], [0, 1265.2202320419033]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "lx1OHcOxDhPq59Nu_j4U1", "type": "text", "x": 9916.315953275642, "y": 900.765230522417, "width": 776.483642578125, "height": 45, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2b", "roundness": null, "seed": 981712078, "version": 2498, "versionNonce": 822022921, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Storing the users history of generated clips", "fontSize": 36, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Storing the users history of generated clips", "autoResize": true, "lineHeight": 1.25}, {"id": "ubdS2H1_nm7fKP_usfmRH", "type": "rectangle", "x": 10351.991411095594, "y": 1020.4983086980321, "width": 277.78916537329224, "height": 101.85682924688672, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2c", "roundness": {"type": 3}, "seed": 493659278, "version": 430, "versionNonce": 847624231, "isDeleted": false, "boundElements": [{"type": "text", "id": "bWcSfAhSvnq2eEIE6WMS3"}, {"id": "klln8eSnCjQa7hDGrGkEQ", "type": "arrow"}, {"id": "fUAkaeFqVXh-eS_N0IOKD", "type": "arrow"}], "updated": 1742299256033, "link": null, "locked": false}, {"id": "bWcSfAhSvnq2eEIE6WMS3", "type": "text", "x": 10399.316078010756, "y": 1058.9267233214755, "width": 183.13983154296875, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2cV", "roundness": null, "seed": 1976706130, "version": 413, "versionNonce": 1904876359, "isDeleted": false, "boundElements": [], "updated": 1742299256033, "link": null, "locked": false, "text": "Generate is clicked", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "ubdS2H1_nm7fKP_usfmRH", "originalText": "Generate is clicked", "autoResize": true, "lineHeight": 1.25}, {"id": "kSGkC5jCf0Tn32GADyr35", "type": "rectangle", "x": 10354.335103299245, "y": 1246.5357253931847, "width": 277.78916537329224, "height": 101.85682924688672, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2e", "roundness": {"type": 3}, "seed": 949528466, "version": 576, "versionNonce": 1242351241, "isDeleted": false, "boundElements": [{"type": "text", "id": "ZBh72idOyEiHG9WmisqKA"}, {"id": "klln8eSnCjQa7hDGrGkEQ", "type": "arrow"}, {"id": "g0Q6IjQOGk_qMS0RSNI0Z", "type": "arrow"}, {"id": "yMZhjePWdsB9sQEaOK9Vc", "type": "arrow"}, {"id": "fUAkaeFqVXh-eS_N0IOKD", "type": "arrow"}], "updated": 1742298167953, "link": null, "locked": false}, {"id": "ZBh72idOyEiHG9WmisqKA", "type": "text", "x": 10452.809725963918, "y": 1272.464140016628, "width": 80.83992004394531, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2f", "roundness": null, "seed": 1107420498, "version": 573, "versionNonce": 195366249, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Next.js\n<PERSON><PERSON>", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "kSGkC5jCf0Tn32GADyr35", "originalText": "Next.js\n<PERSON><PERSON>", "autoResize": true, "lineHeight": 1.25}, {"id": "6FC3NdHKaworI7JyQARv_", "type": "rectangle", "x": 10359.83979987863, "y": 1479.8916065924925, "width": 277.78916537329224, "height": 101.85682924688672, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2g", "roundness": {"type": 3}, "seed": 1244823570, "version": 602, "versionNonce": 451197449, "isDeleted": false, "boundElements": [{"type": "text", "id": "h1O9Dq5X9hDbeaT40sLQo"}, {"id": "g0Q6IjQOGk_qMS0RSNI0Z", "type": "arrow"}, {"id": "yMZhjePWdsB9sQEaOK9Vc", "type": "arrow"}], "updated": 1742298167953, "link": null, "locked": false}, {"id": "h1O9Dq5X9hDbeaT40sLQo", "type": "text", "x": 10439.924407894865, "y": 1518.3200212159359, "width": 117.61994934082031, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2h", "roundness": null, "seed": 600616402, "version": 615, "versionNonce": 1949470953, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "API on EC2", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "6FC3NdHKaworI7JyQARv_", "originalText": "API on EC2", "autoResize": true, "lineHeight": 1.25}, {"id": "klln8eSnCjQa7hDGrGkEQ", "type": "arrow", "x": 10430.629988389104, "y": 1119.6411112093183, "width": 0.11323212146635342, "height": 126.05489161062019, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2i", "roundness": null, "seed": 197594386, "version": 913, "versionNonce": 1462403591, "isDeleted": false, "boundElements": [], "updated": 1742298168020, "link": null, "locked": false, "points": [[0, 0], [0.11323212146635342, 126.05489161062019]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "g0Q6IjQOGk_qMS0RSNI0Z", "type": "arrow", "x": 10432.404312951332, "y": 1349.5528320668252, "width": 0.6782789985009003, "height": 129.2320140146337, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2j", "roundness": null, "seed": 1117506830, "version": 896, "versionNonce": 1274604615, "isDeleted": false, "boundElements": [], "updated": 1742298168020, "link": null, "locked": false, "points": [[0, 0], [-0.6782789985009003, 129.2320140146337]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "yMZhjePWdsB9sQEaOK9Vc", "type": "arrow", "x": 10550.841667717572, "y": 1479.5436110527548, "width": 2.180491186411018, "height": 130.80306087294548, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2k", "roundness": null, "seed": 1512866574, "version": 942, "versionNonce": 1351966119, "isDeleted": false, "boundElements": [], "updated": 1742298168020, "link": null, "locked": false, "points": [[0, 0], [2.180491186411018, -130.80306087294548]], "lastCommittedPoint": null, "startBinding": {"elementId": "6FC3NdHKaworI7JyQARv_", "focus": 0.3667611259114996, "gap": 1}, "endBinding": {"elementId": "kSGkC5jCf0Tn32GADyr35", "focus": -0.43398960125360175, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "fUAkaeFqVXh-eS_N0IOKD", "type": "arrow", "x": 10532.889316082974, "y": 1246.1212269761986, "width": 1.3886490238546685, "height": 123.41809349154187, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2l", "roundness": null, "seed": 1856268050, "version": 1051, "versionNonce": 341121639, "isDeleted": false, "boundElements": [], "updated": 1742299256033, "link": null, "locked": false, "points": [[0, 0], [1.3886490238546685, -123.41809349154187]], "lastCommittedPoint": null, "startBinding": {"elementId": "kSGkC5jCf0Tn32GADyr35", "focus": 0.27885932848582656, "gap": 1.1887193700104035}, "endBinding": {"elementId": "ubdS2H1_nm7fKP_usfmRH", "focus": -0.31525791494375505, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "LT_evWL57PaLg8rfd88UP", "type": "rectangle", "x": 10740.392634971518, "y": 1361.6104745997452, "width": 277.78916537329224, "height": 101.85682924688672, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2n", "roundness": {"type": 3}, "seed": 63312078, "version": 689, "versionNonce": 1627721257, "isDeleted": false, "boundElements": [{"type": "text", "id": "Ils8Wt_NLihHjtBUJ7IEw"}, {"id": "hHwXq2QzJW8WsHgYom0kx", "type": "arrow"}], "updated": 1742298167953, "link": null, "locked": false}, {"id": "Ils8Wt_NLihHjtBUJ7IEw", "type": "text", "x": 10798.737283270957, "y": 1375.0388892231886, "width": 161.09986877441406, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2o", "roundness": null, "seed": 44484366, "version": 784, "versionNonce": 393437449, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Save clip url \nin database, and\nrelate to user", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "LT_evWL57PaLg8rfd88UP", "originalText": "Save clip url \nin database, and\nrelate to user", "autoResize": true, "lineHeight": 1.25}, {"id": "hHwXq2QzJW8WsHgYom0kx", "type": "arrow", "x": 10553.603990797244, "y": 1416.1536488557467, "width": 185.85114798530958, "height": 0.2987113090432558, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2p", "roundness": null, "seed": 187757394, "version": 673, "versionNonce": 965921543, "isDeleted": false, "boundElements": [], "updated": 1742298168020, "link": null, "locked": false, "points": [[0, 0], [185.85114798530958, 0.2987113090432558]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "LT_evWL57PaLg8rfd88UP", "focus": -0.08090095298693994, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "PsOnpaph20UrRNGVZcHZU", "type": "text", "x": 10249.294480696148, "y": 1167.0239338138044, "width": 158.3998565673828, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2q", "roundness": null, "seed": 1587186962, "version": 361, "versionNonce": 515549479, "isDeleted": false, "boundElements": [], "updated": 1742298168020, "link": null, "locked": false, "text": "Spinner is shown", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Spinner is shown", "autoResize": true, "lineHeight": 1.25}, {"id": "2pzpfG370qr1OJjnN85Vj", "type": "text", "x": 10320.857974893606, "y": 1385.6941276197736, "width": 77.41995239257812, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2r", "roundness": null, "seed": 1376711310, "version": 620, "versionNonce": 985779047, "isDeleted": false, "boundElements": [], "updated": 1742298168020, "link": null, "locked": false, "text": "Another\nrequest", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Another\nrequest", "autoResize": true, "lineHeight": 1.25}, {"id": "_BLYpi5qLa0zEhnU6hOGM", "type": "rectangle", "x": 9924.347115127463, "y": 1368.2325217801892, "width": 277.78916537329224, "height": 85, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2s", "roundness": {"type": 3}, "seed": 1156147342, "version": 680, "versionNonce": 1457886505, "isDeleted": false, "boundElements": [{"type": "text", "id": "kEL6xtQPvXMozLuJzPIAB"}], "updated": 1742298167953, "link": null, "locked": false}, {"id": "kEL6xtQPvXMozLuJzPIAB", "type": "text", "x": 9936.091803099753, "y": 1373.2325217801892, "width": 254.29978942871094, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2t", "roundness": null, "seed": 1024009934, "version": 779, "versionNonce": 766183433, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Hides request to EC2\nfrom user, making instance\nless likely to be abused", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "_BLYpi5qLa0zEhnU6hOGM", "originalText": "Hides request to EC2\nfrom user, making instance less likely to be abused", "autoResize": true, "lineHeight": 1.25}, {"id": "SxI8DCRcA9t9VwUEjSLuC", "type": "line", "x": 10311.872203123103, "y": 1409.9165344814328, "width": 107.62480676373161, "height": 1.7201684773899615, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2u", "roundness": {"type": 2}, "seed": 1705112014, "version": 421, "versionNonce": 889134825, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "points": [[0, 0], [-107.62480676373161, 1.7201684773899615]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "hIxt_Y5CDlKI2pGY1NjDx", "type": "text", "x": 10543.29861680965, "y": 1164.259679725222, "width": 228.43983459472656, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2v", "roundness": null, "seed": 205973838, "version": 489, "versionNonce": 728586697, "isDeleted": false, "boundElements": [{"id": "fUAkaeFqVXh-eS_N0IOKD", "type": "arrow"}], "updated": 1742298167953, "link": null, "locked": false, "text": "Stop spinner\nPlay audio clip returned", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Stop spinner\nPlay audio clip returned", "autoResize": true, "lineHeight": 1.25}, {"id": "MiKcom5ZdEGD2v1w5V9-x", "type": "text", "x": 3157.9938479539805, "y": 915.2620664054297, "width": 199.9439239501953, "height": 45, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2w", "roundness": null, "seed": 1386153426, "version": 1109, "versionNonce": 1913731241, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Fine Tuning", "fontSize": 36, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Fine Tuning", "autoResize": true, "lineHeight": 1.25}, {"id": "qTU_hIFrO6nQiWOeOFKLT", "type": "text", "x": 3160.0975298757744, "y": 1203.0159202463494, "width": 418.97967529296875, "height": 150, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2x", "roundness": null, "seed": 2092621070, "version": 666, "versionNonce": 982924169, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Uses pre-trained model\n-Adapt existing model to specific use case,\ninstead of training from the ground up\n-Lesser resource usage\n-Benefit of using model that experts\ntrained before you", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Uses pre-trained model\n-Adapt existing model to specific use case,\ninstead of training from the ground up\n-Lesser resource usage\n-Benefit of using model that experts\ntrained before you", "autoResize": true, "lineHeight": 1.25}, {"id": "CbOb6fCjoHeP88IOTmwSU", "type": "text", "x": 3165.9179256625243, "y": 1375.4693391385733, "width": 465.69976806640625, "height": 100, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b2z", "roundness": null, "seed": 757307282, "version": 580, "versionNonce": 1966596713, "isDeleted": false, "boundElements": [{"id": "U58OlgLMX4L_f9tUXms2A", "type": "arrow"}, {"id": "PtPCJNFOrYYiiimy6OYuv", "type": "arrow"}], "updated": 1742298167953, "link": null, "locked": false, "text": "E.g. text-to-speech model has been knows both\n-How to convert text to speech\n-How do use different voices\n", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "E.g. text-to-speech model has been knows both\n-How to convert text to speech\n-How do use different voices\n", "autoResize": true, "lineHeight": 1.25}, {"id": "dfA3veb4QMDrFozUBT0iZ", "type": "text", "x": 3248.425820200604, "y": 1489.303129267628, "width": 337.81976318359375, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b32", "roundness": null, "seed": 402910610, "version": 549, "versionNonce": 244259881, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "We are only interested in this part", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "We are only interested in this part", "autoResize": true, "lineHeight": 1.25}, {"id": "U58OlgLMX4L_f9tUXms2A", "type": "arrow", "x": 3375.058627533902, "y": 1480.554743197378, "width": 61.22573711781183, "height": 30.12828770766373, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b33", "roundness": null, "seed": 1474780626, "version": 988, "versionNonce": 1686875913, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "points": [[0, 0], [-61.22573711781183, -30.12828770766373]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "6KTxTUQZ_TPUgxy_KQ5kB", "type": "text", "x": 3031.557020381524, "y": 1552.7542898449267, "width": 310.77972412109375, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b34", "roundness": null, "seed": 1545339218, "version": 690, "versionNonce": 926082537, "isDeleted": false, "boundElements": [{"id": "PtPCJNFOrYYiiimy6OYuv", "type": "arrow"}], "updated": 1742298167953, "link": null, "locked": false, "text": "Because this already works fine", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Because this already works fine", "autoResize": true, "lineHeight": 1.25}, {"id": "PtPCJNFOrYYiiimy6OYuv", "type": "arrow", "x": 3090.351051713065, "y": 1537.8098345441424, "width": 73.98509832801346, "height": 124.63516363700592, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b35", "roundness": null, "seed": 963178898, "version": 1283, "versionNonce": 1168307527, "isDeleted": false, "boundElements": [], "updated": 1742298168021, "link": null, "locked": false, "points": [[0, 0], [73.98509832801346, -124.63516363700592]], "lastCommittedPoint": null, "startBinding": {"elementId": "6KTxTUQZ_TPUgxy_KQ5kB", "focus": -0.6933677655793058, "gap": 14.944455300784284}, "endBinding": {"elementId": "CbOb6fCjoHeP88IOTmwSU", "focus": 0.9207687908445927, "gap": 1.5817756214460132}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "PZVrrTT2EFJkH5g9t5vdB", "type": "text", "x": 3164.417184024952, "y": 1686.298467709297, "width": 287.63983154296875, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b36", "roundness": null, "seed": 1182145230, "version": 1042, "versionNonce": 879452809, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Text to speech (StyleTTS2)\n", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Text to speech (StyleTTS2)\n", "autoResize": true, "lineHeight": 1.25}, {"id": "f6kUjRsNtVkRqn57ZdM7B", "type": "text", "x": 3167.9949941911336, "y": 1731.8023783796427, "width": 463.49957275390625, "height": 125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b37", "roundness": null, "seed": 258729870, "version": 1402, "versionNonce": 1741695337, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Finetuning:\n10-15 mins of audio, high-quality, ex studio mic\nTranscription (srt)\nSegmentation of audio into 1-10s parts\nSpecification of validation and training dataset", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Finetuning:\n10-15 mins of audio, high-quality, ex studio mic\nTranscription (srt)\nSegmentation of audio into 1-10s parts\nSpecification of validation and training dataset", "autoResize": true, "lineHeight": 1.25}, {"id": "zXd9n0aoPwww601r8JkBa", "type": "text", "x": 3169.4202435452225, "y": 1935.6889360630755, "width": 255.37979125976562, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b38", "roundness": null, "seed": 1556269774, "version": 1217, "versionNonce": 616109129, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Voice conversion (seed-vc)\n", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Voice conversion (seed-vc)\n", "autoResize": true, "lineHeight": 1.25}, {"id": "ZjMKZCUlwQrN32MJmGQTi", "type": "text", "x": 3168.39479644866, "y": 1981.4190873730452, "width": 483.9996643066406, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b39", "roundness": null, "seed": 1341931790, "version": 1599, "versionNonce": 1862378281, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Finetuning:\nUse the same 10-15 mins of data\nSpeaker labelling not required, just the audio files", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Finetuning:\nUse the same 10-15 mins of data\nSpeaker labelling not required, just the audio files", "autoResize": true, "lineHeight": 1.25}, {"id": "bV-EVb0OPCMSy67Yor8L0", "type": "rectangle", "x": 3182.2805803758033, "y": 2576.7491091402217, "width": 253.46920870177127, "height": 302.2020529111286, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3D", "roundness": {"type": 3}, "seed": 1438994702, "version": 1144, "versionNonce": 1125471753, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false}, {"id": "G53D2j7HrhX5vFqAoKmA-", "type": "text", "x": 3199.480297878159, "y": 2586.881034564588, "width": 99.16790771484375, "height": 20, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3F", "roundness": null, "seed": 1846063058, "version": 1055, "versionNonce": 66039017, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "styletts2-ft", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "styletts2-ft", "autoResize": true, "lineHeight": 1.25}, {"id": "rQ3I8rxzCZaQtt6mA1qXs", "type": "rectangle", "x": 3227.7298883624685, "y": 2627.0664088825843, "width": 163.4188199882414, "height": 56.171881256580036, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3G", "roundness": {"type": 3}, "seed": 1981700046, "version": 1202, "versionNonce": 689711049, "isDeleted": false, "boundElements": [{"type": "text", "id": "4pAFng801-1Pfu5GdmLsC"}], "updated": 1742298167953, "link": null, "locked": false}, {"id": "4pAFng801-1Pfu5GdmLsC", "type": "text", "x": 3241.591367448386, "y": 2645.152349510874, "width": 135.69586181640625, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3H", "roundness": null, "seed": 1872251086, "version": 1158, "versionNonce": 750143145, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Original repo files", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "rQ3I8rxzCZaQtt6mA1qXs", "originalText": "Original repo files", "autoResize": true, "lineHeight": 1.25}, {"id": "vj5G0yJJBh6eFUsfLJJlN", "type": "rectangle", "x": 3227.8957215526625, "y": 2709.271767298559, "width": 163.4188199882414, "height": 56.171881256580036, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3I", "roundness": {"type": 3}, "seed": 1482994190, "version": 1333, "versionNonce": 963217801, "isDeleted": false, "boundElements": [{"type": "text", "id": "HP2Q2DUhBlJzYJCP7Cb7c"}], "updated": 1742298167953, "link": null, "locked": false}, {"id": "HP2Q2DUhBlJzYJCP7Cb7c", "type": "text", "x": 3276.6611618202205, "y": 2727.357707926849, "width": 65.887939453125, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3J", "roundness": null, "seed": 626999374, "version": 1300, "versionNonce": 410087529, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Dataset", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "vj5G0yJJBh6eFUsfLJJlN", "originalText": "Dataset", "autoResize": true, "lineHeight": 1.25}, {"id": "34fGwnD0Jtd5AoB3eoszc", "type": "rectangle", "x": 3228.6988952790107, "y": 2793.6249638799372, "width": 163.4188199882414, "height": 56.171881256580036, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3K", "roundness": {"type": 3}, "seed": 660203726, "version": 1414, "versionNonce": 1603894089, "isDeleted": false, "boundElements": [{"type": "text", "id": "IKVDJTDjo1VAYgufOHanF"}], "updated": 1742298167953, "link": null, "locked": false}, {"id": "IKVDJTDjo1VAYgufOHanF", "type": "text", "x": 3271.296328649596, "y": 2811.710904508227, "width": 78.22395324707031, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3L", "roundness": null, "seed": 905944846, "version": 1392, "versionNonce": 1597844009, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Dockerfile", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "34fGwnD0Jtd5AoB3eoszc", "originalText": "Dockerfile", "autoResize": true, "lineHeight": 1.25}, {"id": "QjZlCaXKUucMDMDWA4sWc", "type": "text", "x": 3188.499811558182, "y": 2541.0757311527914, "width": 195.9358367919922, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3M", "roundness": null, "seed": 1609910734, "version": 1170, "versionNonce": 1346895113, "isDeleted": false, "boundElements": [{"id": "x8VcZ7xEoGyGNuD4ApSUN", "type": "arrow"}], "updated": 1742298167953, "link": null, "locked": false, "text": "Docker image for training", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Docker image for training", "autoResize": true, "lineHeight": 1.25}, {"id": "xt_5Px6g-5GphxIuAbBDa", "type": "text", "x": 3177.6567082096703, "y": 2203.8079548431397, "width": 381.44384765625, "height": 35, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3N", "roundness": null, "seed": 755897550, "version": 895, "versionNonce": 1225854665, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Finetuning job (One-off job)", "fontSize": 28, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Finetuning job (One-off job)", "autoResize": true, "lineHeight": 1.25}, {"id": "OqDOKuwbIp46-csHk6CmM", "type": "rectangle", "x": 3647.7041296543366, "y": 2363.5491742450404, "width": 247.07864288107334, "height": 95.4739054509527, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3O", "roundness": {"type": 3}, "seed": 1190149966, "version": 1144, "versionNonce": 1802915241, "isDeleted": false, "boundElements": [{"type": "text", "id": "5s7hdfHPdqPeq1JYUMxUt"}, {"id": "hC9jAzKNVZJehKGF8CqcU", "type": "arrow"}, {"id": "OReknBXTjQ9iU3DUhQpfX", "type": "arrow"}, {"id": "3JhZ54zET_zjEhZ0DWyXS", "type": "arrow"}], "updated": 1742298167953, "link": null, "locked": false}, {"id": "5s7hdfHPdqPeq1JYUMxUt", "type": "text", "x": 3721.6594972374514, "y": 2391.286126970517, "width": 99.16790771484375, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3OG", "roundness": null, "seed": 1220764178, "version": 779, "versionNonce": 1351659657, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "<repository>\nstyletts2-ft", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "OqDOKuwbIp46-csHk6CmM", "originalText": "<repository>\nstyletts2-ft", "autoResize": true, "lineHeight": 1.25}, {"id": "6N_x_Z_5W4xqewv8dODQ6", "type": "rectangle", "x": 4030.6855885034834, "y": 2362.8497048588424, "width": 247.07864288107334, "height": 95.4739054509527, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3Q", "roundness": {"type": 3}, "seed": 1437759314, "version": 1259, "versionNonce": 1986191945, "isDeleted": false, "boundElements": [{"type": "text", "id": "6Q7fs-N-46pQ_Zx5DcO71"}, {"id": "3JhZ54zET_zjEhZ0DWyXS", "type": "arrow"}, {"id": "H1whQiC_BhtKozNOMvJdz", "type": "arrow"}], "updated": 1742298167953, "link": null, "locked": false}, {"id": "6Q7fs-N-46pQ_Zx5DcO71", "type": "text", "x": 4079.7369643873794, "y": 2400.5866575843193, "width": 148.97589111328125, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3Q8", "roundness": null, "seed": 1128789650, "version": 721, "versionNonce": 1178710313, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Start new instance", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "6N_x_Z_5W4xqewv8dODQ6", "originalText": "Start new instance", "autoResize": true, "lineHeight": 1.25}, {"id": "r4bL9h7if_xvI0ZyNcdM1", "type": "text", "x": 3481.7844032895546, "y": 2380.3116595017555, "width": 116.76789855957031, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3S", "roundness": null, "seed": 1281886930, "version": 848, "versionNonce": 321102857, "isDeleted": false, "boundElements": [], "updated": 1742298167953, "link": null, "locked": false, "text": "Upload to ECR", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Upload to ECR", "autoResize": true, "lineHeight": 1.25}, {"id": "3JhZ54zET_zjEhZ0DWyXS", "type": "arrow", "x": 3897.0496977409925, "y": 2415.9182612175646, "width": 132.27255832610876, "height": 0.26262276239640414, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3T", "roundness": null, "seed": 950144974, "version": 1881, "versionNonce": 1682093159, "isDeleted": false, "boundElements": [], "updated": 1742298168021, "link": null, "locked": false, "points": [[0, 0], [132.27255832610876, 0.26262276239640414]], "lastCommittedPoint": null, "startBinding": {"elementId": "OqDOKuwbIp46-csHk6CmM", "focus": 0.09133276192192696, "gap": 2.266925205582538}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "YjDcuqh52ls4hcaKau4mi", "type": "text", "x": 4036.1019738699656, "y": 2333.4264832403123, "width": 78.12791442871094, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3U", "roundness": null, "seed": 369959246, "version": 675, "versionNonce": 141489609, "isDeleted": false, "boundElements": [], "updated": 1742298167954, "link": null, "locked": false, "text": "G5.<PERSON><PERSON><PERSON>ge", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "G5.<PERSON><PERSON><PERSON>ge", "autoResize": true, "lineHeight": 1.25}, {"id": "Z6n3lUiJa779bg0yO2VTN", "type": "rectangle", "x": 4030.473065114315, "y": 2521.066445638937, "width": 247.07864288107334, "height": 95.4739054509527, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3V", "roundness": {"type": 3}, "seed": 457554766, "version": 1451, "versionNonce": 1954501801, "isDeleted": false, "boundElements": [{"type": "text", "id": "7y20KVjwjeS9iB0ym1N0p"}, {"id": "5HqRY9kGdUnNRzdWp4541", "type": "arrow"}, {"id": "hC9jAzKNVZJehKGF8CqcU", "type": "arrow"}, {"id": "H1whQiC_BhtKozNOMvJdz", "type": "arrow"}], "updated": 1742298167954, "link": null, "locked": false}, {"id": "7y20KVjwjeS9iB0ym1N0p", "type": "text", "x": 4036.1724970284845, "y": 2548.8033983644127, "width": 235.67977905273438, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3W", "roundness": null, "seed": 298656142, "version": 941, "versionNonce": 14735241, "isDeleted": false, "boundElements": [], "updated": 1742298167954, "link": null, "locked": false, "text": "Pull and manually start docker\nimage", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "Z6n3lUiJa779bg0yO2VTN", "originalText": "Pull and manually start docker image", "autoResize": true, "lineHeight": 1.25}, {"id": "H1whQiC_BhtKozNOMvJdz", "type": "arrow", "x": 4151.890120706406, "y": 2458.6588666761245, "width": 0, "height": 61.87692127516539, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3X", "roundness": null, "seed": 551944978, "version": 1720, "versionNonce": 316510087, "isDeleted": false, "boundElements": [], "updated": 1742298168021, "link": null, "locked": false, "points": [[0, 0], [0, 61.87692127516539]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "Z6n3lUiJa779bg0yO2VTN", "focus": -0.017178869235294787, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "aoQdaidkI_Z6JGzUErAes", "type": "rectangle", "x": 4036.3817524104275, "y": 2681.5201588162245, "width": 247.07864288107334, "height": 95.4739054509527, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3Y", "roundness": {"type": 3}, "seed": 1640603154, "version": 1837, "versionNonce": 185546793, "isDeleted": false, "boundElements": [{"type": "text", "id": "w7z3d5fUAgsLjcQ2h7tTz"}, {"id": "5HqRY9kGdUnNRzdWp4541", "type": "arrow"}, {"id": "urzLy4M1Mo7-B06sBXk53", "type": "arrow"}], "updated": 1742298167954, "link": null, "locked": false}, {"id": "w7z3d5fUAgsLjcQ2h7tTz", "type": "text", "x": 4063.225158628796, "y": 2709.2571115417013, "width": 193.39183044433594, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3Z", "roundness": null, "seed": 919604178, "version": 1372, "versionNonce": 313019145, "isDeleted": false, "boundElements": [], "updated": 1742298167954, "link": null, "locked": false, "text": "Upload fine-tuned model \nfile to S3 ", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "aoQdaidkI_Z6JGzUErAes", "originalText": "Upload fine-tuned model \nfile to S3 ", "autoResize": true, "lineHeight": 1.25}, {"id": "5HqRY9kGdUnNRzdWp4541", "type": "arrow", "x": 4154.905234081398, "y": 2617.2211080401676, "width": 1.1171770352302701, "height": 63.93462435627953, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3a", "roundness": null, "seed": 1056889298, "version": 1689, "versionNonce": 1538262695, "isDeleted": false, "boundElements": [], "updated": 1742298168021, "link": null, "locked": false, "points": [[0, 0], [-1.1171770352302701, 63.93462435627953]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "aoQdaidkI_Z6JGzUErAes", "focus": -0.056069257588548506, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "2R81DJktfkB54v4-_Fp5y", "type": "rectangle", "x": 4404.169489027173, "y": 2360.7401432831057, "width": 247.07864288107334, "height": 95.4739054509527, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3b", "roundness": {"type": 3}, "seed": 2084284174, "version": 1959, "versionNonce": 1364852361, "isDeleted": false, "boundElements": [{"type": "text", "id": "yXyurWTay3BXSSrYRR8E0"}, {"id": "urzLy4M1Mo7-B06sBXk53", "type": "arrow"}, {"id": "rRWYcrwMPk36iAFCHrsLb", "type": "arrow"}], "updated": 1742298167954, "link": null, "locked": false}, {"id": "yXyurWTay3BXSSrYRR8E0", "type": "text", "x": 4431.012895245542, "y": 2388.4770960085825, "width": 193.39183044433594, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3c", "roundness": null, "seed": 2089784654, "version": 1496, "versionNonce": 613107049, "isDeleted": false, "boundElements": [], "updated": 1742298167954, "link": null, "locked": false, "text": "Upload fine-tuned model \nfile to S3 ", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "2R81DJktfkB54v4-_Fp5y", "originalText": "Upload fine-tuned model \nfile to S3 ", "autoResize": true, "lineHeight": 1.25}, {"id": "9-BIq16QqLMDEUHnt91NO", "type": "text", "x": 3760.4093446597244, "y": 2278.442547921528, "width": 33.1519775390625, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3d", "roundness": null, "seed": 506058258, "version": 622, "versionNonce": 1440084489, "isDeleted": false, "boundElements": [], "updated": 1742298167954, "link": null, "locked": false, "text": "ECR", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ECR", "autoResize": true, "lineHeight": 1.25}, {"id": "oPtX4zq7BNMctV4KcefgT", "type": "text", "x": 4144.904204491375, "y": 2283.620955872856, "width": 32.57598876953125, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3e", "roundness": null, "seed": 1211748302, "version": 751, "versionNonce": 742181097, "isDeleted": false, "boundElements": [], "updated": 1742298167954, "link": null, "locked": false, "text": "EC2", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "EC2", "autoResize": true, "lineHeight": 1.25}, {"id": "hC9jAzKNVZJehKGF8CqcU", "type": "arrow", "x": 3877.449359260602, "y": 2469.634814279977, "width": 137.04975802803636, "height": 104.90724707228219, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3i", "roundness": {"type": 2}, "seed": 2069036430, "version": 1583, "versionNonce": 23446473, "isDeleted": false, "boundElements": [], "updated": 1742298167954, "link": null, "locked": false, "points": [[0, 0], [50.49496849099023, 68.55760117467617], [137.04975802803636, 104.90724707228219]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "BoMR2jVWCW9pFELfWDb0W", "type": "text", "x": 3860.1066141114097, "y": 2558.2791475048098, "width": 74.73591613769531, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3j", "roundness": null, "seed": 348394190, "version": 704, "versionNonce": 23880361, "isDeleted": false, "boundElements": [], "updated": 1742298167954, "link": null, "locked": false, "text": "Pull image", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Pull image", "autoResize": true, "lineHeight": 1.25}, {"id": "urzLy4M1Mo7-B06sBXk53", "type": "arrow", "x": 4288.4603952915, "y": 2729.1571115417, "width": 110.70909373567247, "height": 320.78001553311924, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3k", "roundness": null, "seed": 1523294674, "version": 1986, "versionNonce": 1224484233, "isDeleted": false, "boundElements": [], "updated": 1742298167954, "link": null, "locked": false, "points": [[0, 0], [55.35454686783669, 0], [55.35454686783669, -320.78001553311924], [110.70909373567247, -320.78001553311924]], "lastCommittedPoint": null, "startBinding": {"elementId": "aoQdaidkI_Z6JGzUErAes", "focus": -0.002094813227310389, "gap": 5.000000000000227, "fixedPoint": [1.020236471844336, 0.4989525933863452]}, "endBinding": {"elementId": "2R81DJktfkB54v4-_Fp5y", "focus": 0.0020948132273199147, "gap": 5, "fixedPoint": [-0.020236471844337658, 0.4989525933863404]}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": true, "fixedSegments": null, "startIsSpecial": null, "endIsSpecial": null}, {"id": "bLKrHgqXuu8siTg-VjQao", "type": "text", "x": 4521.746507520499, "y": 2278.512666837223, "width": 19.67999267578125, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3l", "roundness": null, "seed": 136311054, "version": 704, "versionNonce": 1397787753, "isDeleted": false, "boundElements": [], "updated": 1742298167954, "link": null, "locked": false, "text": "S3", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "S3", "autoResize": true, "lineHeight": 1.25}, {"id": "8U6O1fD4UM-FyK3JxSoxt", "type": "rectangle", "x": 3183.500795673908, "y": 2364.2421723551615, "width": 247.07864288107334, "height": 95.4739054509527, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3m", "roundness": {"type": 3}, "seed": 224173710, "version": 1297, "versionNonce": 674568009, "isDeleted": false, "boundElements": [{"type": "text", "id": "OFdbD0UrTr2KOXW9wo6vI"}, {"id": "OReknBXTjQ9iU3DUhQpfX", "type": "arrow"}, {"id": "x8VcZ7xEoGyGNuD4ApSUN", "type": "arrow"}], "updated": 1742298167954, "link": null, "locked": false}, {"id": "OFdbD0UrTr2KOXW9wo6vI", "type": "text", "x": 3203.4802111085855, "y": 2391.9791250806384, "width": 207.11981201171875, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3n", "roundness": null, "seed": 1034263758, "version": 978, "versionNonce": 1241859625, "isDeleted": false, "boundElements": [], "updated": 1742298167954, "link": null, "locked": false, "text": "Build training docker image\nlocally", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "8U6O1fD4UM-FyK3JxSoxt", "originalText": "Build training docker image locally", "autoResize": true, "lineHeight": 1.25}, {"id": "OReknBXTjQ9iU3DUhQpfX", "type": "arrow", "x": 3435.2052453504557, "y": 2411.597380057724, "width": 207.4988843038808, "height": 0.4552249915300308, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3o", "roundness": null, "seed": 1556597070, "version": 1395, "versionNonce": 1298569671, "isDeleted": false, "boundElements": [], "updated": 1742298168021, "link": null, "locked": false, "points": [[0, 0], [207.4988843038808, -0.4552249915300308]], "lastCommittedPoint": null, "startBinding": {"elementId": "8U6O1fD4UM-FyK3JxSoxt", "focus": -0.0020948132272947404, "gap": 4.625806795474091}, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false, "fixedSegments": null, "startIsSpecial": null, "endIsSpecial": null}, {"id": "x8VcZ7xEoGyGNuD4ApSUN", "type": "arrow", "x": 3301.8148745439967, "y": 2460.881698301824, "width": 1.7201774849759204, "height": 66.42365887037067, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3p", "roundness": null, "seed": 434307214, "version": 1630, "versionNonce": 1751258119, "isDeleted": false, "boundElements": [], "updated": 1742298168021, "link": null, "locked": false, "points": [[0, 0], [-1.7201774849759204, 66.42365887037067]], "lastCommittedPoint": null, "startBinding": {"elementId": "8U6O1fD4UM-FyK3JxSoxt", "focus": 0.03172744272991391, "gap": 1.1656204957098453}, "endBinding": {"elementId": "QjZlCaXKUucMDMDWA4sWc", "focus": 0.13246255108401891, "gap": 13.77037398059656}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "LLRUSy-vpNrqv3sw6Wwbv", "type": "rectangle", "x": 3185.2364401383657, "y": 2980.0593365838386, "width": 247.07864288107334, "height": 95.4739054509527, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3q", "roundness": {"type": 3}, "seed": 1734541074, "version": 1464, "versionNonce": 1858555017, "isDeleted": false, "boundElements": [{"type": "text", "id": "3AKu00FPSnKkY0xG6ukhg"}, {"id": "kgWzmk-hiREdntHQkxlgu", "type": "arrow"}, {"id": "rRWYcrwMPk36iAFCHrsLb", "type": "arrow"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "3AKu00FPSnKkY0xG6ukhg", "type": "text", "x": 3198.8558549626914, "y": 3007.7962893093154, "width": 219.83981323242188, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3r", "roundness": null, "seed": 1687506130, "version": 1188, "versionNonce": 52351849, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Download model file from S3\nbucket", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "LLRUSy-vpNrqv3sw6Wwbv", "originalText": "Download model file from S3 bucket", "autoResize": true, "lineHeight": 1.25}, {"id": "rRWYcrwMPk36iAFCHrsLb", "type": "arrow", "x": 4535.25529653767, "y": 2456.578475153836, "width": 1101.91026197961, "height": 573.9428517758988, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3s", "roundness": {"type": 2}, "seed": 952118290, "version": 1937, "versionNonce": 1576271431, "isDeleted": false, "boundElements": [], "updated": 1742298168022, "link": null, "locked": false, "points": [[0, 0], [-119.77878146038279, 509.56640439889634], [-1101.91026197961, 573.9428517758988]], "lastCommittedPoint": null, "startBinding": {"elementId": "2R81DJktfkB54v4-_Fp5y", "focus": -0.1399016984195189, "gap": 1}, "endBinding": {"elementId": "LLRUSy-vpNrqv3sw6Wwbv", "focus": 0.1950448616273897, "gap": 1.029951538620935}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "me08lYG3cTtrEglhwlCC_", "type": "rectangle", "x": 2725.3038498665474, "y": 2674.8034589240397, "width": 273.2110790773333, "height": 95.4739054509527, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3t", "roundness": {"type": 3}, "seed": 744958354, "version": 1453, "versionNonce": 1126973449, "isDeleted": false, "boundElements": [{"type": "text", "id": "wl-ybN4HdufrSQRIEzury"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "wl-ybN4HdufrSQRIEzury", "type": "text", "x": 2745.189494996034, "y": 2702.5404116495165, "width": 233.43978881835938, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3u", "roundness": null, "seed": 2091919186, "version": 1213, "versionNonce": 1972983529, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "NOTE: dataset and model file\nare baked into docker image", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "me08lYG3cTtrEglhwlCC_", "originalText": "NOTE: dataset and model file are baked into docker image", "autoResize": true, "lineHeight": 1.25}, {"id": "jDxlg8avOjnSBeF42JSrA", "type": "line", "x": 3000.6609073573436, "y": 2728.3831292706363, "width": 181.92469047033524, "height": 0.5763586695038612, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3v", "roundness": {"type": 2}, "seed": 1354424270, "version": 490, "versionNonce": 1275105737, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [181.92469047033524, 0.5763586695038612]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "3nJ5wTdBUgziFPZIVpS2G", "type": "text", "x": 2728.7114986178085, "y": 2796.0718608405223, "width": 202.6558074951172, "height": 120, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3w", "roundness": null, "seed": 1647180302, "version": 589, "versionNonce": 192770217, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "+Ship entire image as one \nrunnable package\n\n-Takes up more space\n-Slower builds and pushes\n-Harder to modify", "fontSize": 16, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "+Ship entire image as one \nrunnable package\n\n-Takes up more space\n-Slower builds and pushes\n-Harder to modify", "autoResize": true, "lineHeight": 1.25}, {"id": "gNwYHMxFttnrLYTvUVBRa", "type": "rectangle", "x": 3186.226441168953, "y": 3142.708760510236, "width": 247.07864288107334, "height": 95.4739054509527, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3x", "roundness": {"type": 3}, "seed": 178596306, "version": 1528, "versionNonce": 1237021577, "isDeleted": false, "boundElements": [{"type": "text", "id": "tA9fSQbbnNupO0k5o4nQg"}, {"id": "kgWzmk-hiREdntHQkxlgu", "type": "arrow"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "tA9fSQbbnNupO0k5o4nQg", "type": "text", "x": 3196.541847509392, "y": 3170.445713235713, "width": 226.4478302001953, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3y", "roundness": null, "seed": 465837970, "version": 1289, "versionNonce": 1526078057, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Run inference with new model\nfile", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "gNwYHMxFttnrLYTvUVBRa", "originalText": "Run inference with new model file", "autoResize": true, "lineHeight": 1.25}, {"id": "kgWzmk-hiREdntHQkxlgu", "type": "arrow", "x": 3309.87546709518, "y": 3076.2696532169357, "width": 0.12702104376285206, "height": 66.07468087352345, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b3z", "roundness": null, "seed": 1743643154, "version": 1002, "versionNonce": 1066048871, "isDeleted": false, "boundElements": [], "updated": 1742298168022, "link": null, "locked": false, "points": [[0, 0], [-0.12702104376285206, 66.07468087352345]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "gNwYHMxFttnrLYTvUVBRa", "focus": -0.0008880126943479876, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "Y7BRhsJsMokEL3jsTfrST", "type": "rectangle", "x": -558.2429039916481, "y": 1245.741138396713, "width": 468.6530630100377, "height": 162.05876270620251, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b40", "roundness": {"type": 3}, "seed": 260778958, "version": 539, "versionNonce": **********, "isDeleted": false, "boundElements": [{"type": "text", "id": "ohfRv1vncMCmpqzpNPIav"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "ohfRv1vncMCmpqzpNPIav", "type": "text", "x": -542.556234547176, "y": 1289.2705197498142, "width": 437.27972412109375, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b40G", "roundness": null, "seed": **********, "version": 680, "versionNonce": 269819369, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "All of these models have existing model files,\ntrained by the researchers. Although fine-\ntuning is possible, it is not required.", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "Y7BRhsJsMokEL3jsTfrST", "originalText": "All of these models have existing model files, trained by the researchers. Although fine-tuning is possible, it is not required.", "autoResize": true, "lineHeight": 1.25}, {"id": "kY83Oe9pZmidFmzLYhbnG", "type": "rectangle", "x": 4038.794804796843, "y": 2838.795961149889, "width": 247.07864288107334, "height": 95.4739054509527, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b42", "roundness": {"type": 3}, "seed": 44672078, "version": 1903, "versionNonce": 1365997769, "isDeleted": false, "boundElements": [{"type": "text", "id": "ZO7Du7YVpPoWAhq7tpCsS"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "ZO7Du7YVpPoWAhq7tpCsS", "type": "text", "x": 4088.4621856245867, "y": 2876.532913875366, "width": 147.74388122558594, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b43", "roundness": null, "seed": 382462606, "version": 1467, "versionNonce": 284391337, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Terminate instance", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "kY83Oe9pZmidFmzLYhbnG", "originalText": "Terminate instance", "autoResize": true, "lineHeight": 1.25}, {"id": "vKLSm_cIBtLDOCz_8F0hJ", "type": "arrow", "x": 4155.170418420378, "y": 2774.1818960396076, "width": 1.1060711874179106, "height": 63.2990507760569, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b45", "roundness": null, "seed": 636211406, "version": 1574, "versionNonce": 1624808073, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [-1.1060711874179106, 63.2990507760569]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "_cIVwPZ0AiWJzE-9HPsLx", "type": "text", "x": 3827.94816248109, "y": 1195.162456386933, "width": 401.75970458984375, "height": 125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4A", "roundness": null, "seed": 101717518, "version": 913, "versionNonce": 30826857, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Estimated fine-tuning costs:\n~ $5-10 USD\n\nG5.Xlarge on-demand: $1.006 USD / hour\nFinetuning task: a couple of hours", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Estimated fine-tuning costs:\n~ $5-10 USD\n\nG5.Xlarge on-demand: $1.006 USD / hour\nFinetuning task: a couple of hours", "autoResize": true, "lineHeight": 1.25}, {"id": "-5-vpZEc-KVqKoymS-Pk1", "type": "text", "x": 3215.7362334752256, "y": 3657.895586159574, "width": 94.24797058105469, "height": 35, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4K", "roundness": null, "seed": 1341437266, "version": 1206, "versionNonce": 288902217, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "<PERSON>er", "fontSize": 28, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "<PERSON>er", "autoResize": true, "lineHeight": 1.25}, {"id": "tereRvE4rcJpgY8W3sVP-", "type": "text", "x": 3229.3653419195734, "y": 4373.731012884976, "width": 566.839599609375, "height": 100, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4L", "roundness": null, "seed": 1186015054, "version": 1441, "versionNonce": 1993988905, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Isolated environments that run the same on all computers\n-When a docker container has been built, it can run on any\nsystem with docker installed. Your local pc, in the cloud,\nyour friends pc, etc.", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Isolated environments that run the same on all computers\n-When a docker container has been built, it can run on any\nsystem with docker installed. Your local pc, in the cloud,\nyour friends pc, etc.", "autoResize": true, "lineHeight": 1.25}, {"id": "1PETjzQCQLQIqRwf3_PqM", "type": "rectangle", "x": 3222.8059561574064, "y": 3870.944732873567, "width": 187.36028573051192, "height": 85.94698368916825, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4M", "roundness": {"type": 3}, "seed": 211424018, "version": 935, "versionNonce": 321293833, "isDeleted": false, "boundElements": [{"type": "text", "id": "kjF8yc6IzZniOtOaojlvD"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "kjF8yc6IzZniOtOaojlvD", "type": "text", "x": 3278.5901334464907, "y": 3903.9182247181516, "width": 75.79193115234375, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4MV", "roundness": null, "seed": 1320563598, "version": 674, "versionNonce": 756671721, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "styletts2", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "1PETjzQCQLQIqRwf3_PqM", "originalText": "styletts2", "autoResize": true, "lineHeight": 1.25}, {"id": "HE04nykuhzRRevj1fDzF6", "type": "rectangle", "x": 3437.4328270817728, "y": 3870.77379788739, "width": 187.36028573051192, "height": 85.94698368916825, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4O", "roundness": {"type": 3}, "seed": 983345554, "version": 1050, "versionNonce": 541024201, "isDeleted": false, "boundElements": [{"type": "text", "id": "-L-Q-zybclj8qFf2wfegI"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "-L-Q-zybclj8qFf2wfegI", "type": "text", "x": 3501.8169952155836, "y": 3903.747289731975, "width": 58.591949462890625, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4P", "roundness": null, "seed": 674604882, "version": 798, "versionNonce": 1253211817, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "seed-vc", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "HE04nykuhzRRevj1fDzF6", "originalText": "seed-vc", "autoResize": true, "lineHeight": 1.25}, {"id": "OKGzvBGPNLHmyetUmfJnp", "type": "rectangle", "x": 3651.163269120873, "y": 3870.6637308962163, "width": 187.36028573051192, "height": 85.94698368916825, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4Q", "roundness": {"type": 3}, "seed": 1629434450, "version": 1132, "versionNonce": 991871369, "isDeleted": false, "boundElements": [{"type": "text", "id": "AikfO95IAN0ZEUgNOgq55"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "AikfO95IAN0ZEUgNOgq55", "type": "text", "x": 3690.3954673450157, "y": 3903.637222740801, "width": 108.89588928222656, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4R", "roundness": null, "seed": 384374802, "version": 894, "versionNonce": 515876969, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "make-an-audio", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "OKGzvBGPNLHmyetUmfJnp", "originalText": "make-an-audio", "autoResize": true, "lineHeight": 1.25}, {"id": "8VgFbxiv109FSYzL9vNKN", "type": "text", "x": 3262.415858708679, "y": 3964.114731085765, "width": 106.47990417480469, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4S", "roundness": null, "seed": 322035790, "version": 629, "versionNonce": 1868608329, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Requires \npytorch 2.6.0", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "Requires \npytorch 2.6.0", "autoResize": true, "lineHeight": 1.25}, {"id": "Js8LBs_Iouu-IwUC-0fZe", "type": "text", "x": 3478.3373556541706, "y": 3963.683069730827, "width": 105.96791076660156, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4T", "roundness": null, "seed": 1435997710, "version": 729, "versionNonce": 1633509929, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Requires \npytorch 2.3.0", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "Requires \npytorch 2.3.0", "autoResize": true, "lineHeight": 1.25}, {"id": "14mqD5v1yCkunCGoc1xpd", "type": "text", "x": 3696.7093563438552, "y": 3962.8929459727597, "width": 107.43991088867188, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4U", "roundness": null, "seed": 380227150, "version": 810, "versionNonce": 295777545, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Requires \npytorch 2.2.0", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "Requires \npytorch 2.2.0", "autoResize": true, "lineHeight": 1.25}, {"id": "HFqzeOsglrIH83kO3FptK", "type": "line", "x": 3261.7790425454614, "y": 4008.8923416735306, "width": 111.41531181538357, "height": 1.1674962992333349, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4V", "roundness": {"type": 2}, "seed": 799396238, "version": 560, "versionNonce": 1940368361, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [111.41531181538357, -1.1674962992333349]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "yFaGYwj0_Y1ZZ2ljmm3M9", "type": "line", "x": 3477.1251107764297, "y": 4009.006339628675, "width": 107.50400266475344, "height": 0.4874395323399767, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4W", "roundness": {"type": 2}, "seed": 408656594, "version": 569, "versionNonce": 2138851017, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [107.50400266475344, -0.4874395323399767]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "qQ14rN2pbgLVLI5MdCJSt", "type": "line", "x": 3697.7347397638737, "y": 4008.8844797455895, "width": 114.20236527045563, "height": 0, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4X", "roundness": {"type": 2}, "seed": 251741394, "version": 530, "versionNonce": 532317609, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [114.20236527045563, 0]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "DzPJ0rN9uKOr3F-rfPVd4", "type": "freedraw", "x": 3419.552212464744, "y": 4004.9653086670187, "width": 15.853577692955696, "height": 14.921939231951455, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4Y", "roundness": null, "seed": 2046194318, "version": 526, "versionNonce": 1603135625, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [0.3498557933730808, 0], [1.3404587139343676, -0.16510048676036604], [2.9010514102160414, -0.6918496588050402], [4.787914116047432, -1.6234881198092808], [6.733741281436323, -2.9757397256553304], [8.49088217624194, -4.528470493995883], [9.552242448272409, -5.585899802055337], [10.582155008538848, -6.619743326292337], [11.639584316598302, -7.838342157142051], [12.697013624658211, -9.056940987991311], [13.915612455507926, -10.48388090927665], [14.226158609175855, -11.14821382028822], [14.446292591522706, -11.78896094747688], [14.650702717988224, -12.366812651137934], [14.819734168718696, -12.905354715093836], [14.953386943715032, -13.196246048909416], [15.0791777907707, -13.337760751846872], [15.212830565767035, -13.601135337869437], [15.34255237679281, -13.864509923891546], [15.476205151789145, -14.135746437854777], [15.727786845900027, -14.403051987847448], [15.853577692955696, -14.666426573870012], [15.853577692955696, -14.921939231951455], [15.853577692955696, -14.921939231951455]], "pressures": [], "simulatePressure": true, "lastCommittedPoint": null}, {"id": "LYdgFYuYS-bth-GlRgWa-", "type": "freedraw", "x": 3419.8431037985597, "y": 3990.043369435067, "width": 19.733439131821797, "height": 18.876488986256845, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4Z", "roundness": null, "seed": 948186578, "version": 534, "versionNonce": 1488056169, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [0.23192687425853364, 0], [0.9395003889453619, 0], [1.8829317418612845, 0], [2.6258839322822496, 0], [3.5182127535817926, 0], [4.622813629287521, 0.23585783822909434], [5.770655108668507, 0.7665379742443292], [6.580433686587639, 1.2264607587908358], [7.920892400522462, 2.5472646528728546], [9.119836411519373, 3.887723366807222], [9.591552087977107, 4.489160854291185], [10.377744882073785, 5.377558711620168], [11.635653352628196, 7.264421417451558], [12.075921317322354, 8.113509635075843], [12.355019759226252, 8.821083149762671], [13.042938454060732, 10.055405836494174], [13.742650040806438, 11.281866595284555], [14.076781978297731, 12.335364939373903], [14.556359582696587, 13.608997265810103], [14.992696583420184, 14.273330176821673], [15.263933097383415, 14.690012357692922], [15.405447800320871, 15.10669453856417], [15.829991909132787, 15.660960458402315], [16.67121819881595, 16.41570554073496], [17.11541712748067, 16.74197555028468], [18.00381498480965, 17.445618101000946], [18.4204971656809, 17.73257847084642], [19.02979658110553, 18.29470631862523], [19.30496405903932, 18.565942832588462], [19.584062500943674, 18.727112355378267], [19.733439131821797, 18.876488986256845], [19.733439131821797, 18.876488986256845]], "pressures": [], "simulatePressure": true, "lastCommittedPoint": null}, {"id": "YvTjI2tgVyOBXhCXLfgE7", "type": "freedraw", "x": 3639.1869623875086, "y": 4006.35686991257, "width": 14.906215376069213, "height": 20.232671556073, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4a", "roundness": null, "seed": 995637454, "version": 533, "versionNonce": 1634867785, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [0, -0.23192687425853364], [0.12579084705521382, -0.8294333977719361], [0.5188872441035528, -1.635281011720508], [1.1399795514398647, -2.9482229778618603], [1.831829210244905, -4.359439043264956], [2.3389235624367757, -5.251767864564499], [2.8538798425702225, -6.285611388801499], [3.5221437175523533, -7.472762507887182], [4.170752772681681, -8.47122735638959], [5.031633882217648, -9.512932808567712], [6.3681616321814545, -11.15214478425878], [7.1700782821599205, -11.957992398207352], [7.7439990218504136, -12.535844101868406], [8.17247409463289, -13.11369580552946], [9.05301002402075, -14.147539329766005], [9.481485096803226, -14.579945366519496], [9.898167277674474, -15.00055851136085], [10.330573314427966, -15.436895512084448], [10.912355982059125, -16.144469026771276], [11.509862505572528, -16.883490253222135], [12.099507101144809, -17.508513524528553], [12.697013624658211, -18.011676912750318], [13.168729301115945, -18.483392589208506], [13.506792202577344, -18.93152248184333], [13.809476428304606, -19.24993056345238], [14.088574870208959, -19.54082189726796], [14.35981138417219, -19.69806045608766], [14.62711693416486, -19.843506122995223], [14.772562601072877, -20.102949745047226], [14.906215376069213, -20.232671556073], [14.906215376069213, -20.232671556073]], "pressures": [], "simulatePressure": true, "lastCommittedPoint": null}, {"id": "SsqSn2tVtpmUyPCJ8Oyrj", "type": "freedraw", "x": 3635.959640967742, "y": 3985.09428579623, "width": 23.89632997656281, "height": 18.648493075968872, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4b", "roundness": null, "seed": 1454604622, "version": 537, "versionNonce": 2057955625, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [0.11399795514398647, 0], [0.6407471271886607, 0], [1.513421128635855, 0], [2.4568524815517776, 0], [3.632210708725779, -0.16510048676036604], [5.25569882853506, -0.15723855881924464], [6.965668155694857, 0.5660588117493717], [8.502675068153621, 1.682452579366327], [9.949269809291309, 2.590505256548113], [10.951665621764278, 3.404214798437806], [12.009094929823732, 4.434127358704245], [14.006024626829003, 6.045822586601844], [15.436895512084448, 7.276214309362786], [16.136607098830154, 7.877651796846749], [17.453480028942067, 8.935081104906658], [18.216087039215836, 9.544380520331288], [18.852903202433936, 10.145818007815251], [19.426823942123974, 10.593947900450075], [20.000744681814467, 11.474483829838391], [20.165845168574833, 11.777168055565198], [20.32701469136464, 12.095576137174248], [20.861625791350434, 13.054731345971959], [21.152517125166014, 13.636514013603573], [21.451270386922715, 14.234020537116976], [21.753954612649522, 14.99662754739029], [22.05270787440668, 15.755303593693498], [22.339668244251698, 16.32922433338399], [22.626628614096717, 16.903145073074484], [23.15730875011195, 17.57927087599728], [23.28703056113818, 17.787611966432905], [23.385304660399925, 17.99202209289797], [23.515026471426154, 18.302568246566352], [23.660472138333716, 18.483392589208506], [23.89632997656281, 18.483392589208506], [23.89632997656281, 18.483392589208506]], "pressures": [], "simulatePressure": true, "lastCommittedPoint": null}, {"id": "NPpohcDwNa0Zw_NoBnnTN", "type": "rectangle", "x": 3223.5772077938755, "y": 4043.4848246137753, "width": 619.7950892259639, "height": 67.78554270699898, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4c", "roundness": {"type": 3}, "seed": 229983634, "version": 698, "versionNonce": 2110053385, "isDeleted": false, "boundElements": [{"type": "text", "id": "CTwFmPrilctRQ6G7RYQQW"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "CTwFmPrilctRQ6G7RYQQW", "type": "text", "x": 3421.4588374898653, "y": 4057.377595967275, "width": 224.03182983398438, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4d", "roundness": null, "seed": 2140173006, "version": 576, "versionNonce": 180253417, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Shared Python Environment\nOne version per package only", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "NPpohcDwNa0Zw_NoBnnTN", "originalText": "Shared Python Environment\nOne version per package only", "autoResize": true, "lineHeight": 1.25}, {"id": "Wm5-M0JuBcGSqBfqApPn1", "type": "rectangle", "x": 3993.126287480011, "y": 3868.667191602857, "width": 187.36028573051192, "height": 85.94698368916825, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4e", "roundness": {"type": 3}, "seed": 1247151054, "version": 1120, "versionNonce": 908209609, "isDeleted": false, "boundElements": [{"type": "text", "id": "LO02aZKrsiuxmpC95MNV9"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "LO02aZKrsiuxmpC95MNV9", "type": "text", "x": 4048.910464769095, "y": 3901.640683447442, "width": 75.79193115234375, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4f", "roundness": null, "seed": 2025871886, "version": 860, "versionNonce": 1997245609, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "styletts2", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "Wm5-M0JuBcGSqBfqApPn1", "originalText": "styletts2", "autoResize": true, "lineHeight": 1.25}, {"id": "ZHVvGRQ23PzpYt1qky1NY", "type": "rectangle", "x": 4207.753158404377, "y": 3868.4962566166796, "width": 187.36028573051192, "height": 85.94698368916825, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4g", "roundness": {"type": 3}, "seed": 201713742, "version": 1235, "versionNonce": 1428557705, "isDeleted": false, "boundElements": [{"type": "text", "id": "QkOjJv7iD4NJ4bsJ8erdL"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "QkOjJv7iD4NJ4bsJ8erdL", "type": "text", "x": 4272.137326538188, "y": 3901.4697484612643, "width": 58.591949462890625, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4h", "roundness": null, "seed": 872394382, "version": 984, "versionNonce": 1454192233, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "seed-vc", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "ZHVvGRQ23PzpYt1qky1NY", "originalText": "seed-vc", "autoResize": true, "lineHeight": 1.25}, {"id": "cD4ZjOJycVzD2FIgSHGKQ", "type": "rectangle", "x": 4421.483600443477, "y": 3868.3861896255057, "width": 187.36028573051192, "height": 85.94698368916825, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4i", "roundness": {"type": 3}, "seed": 1030139086, "version": 1317, "versionNonce": 778862921, "isDeleted": false, "boundElements": [{"type": "text", "id": "JAhA4fzqN6v-aWyvv59bh"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "JAhA4fzqN6v-aWyvv59bh", "type": "text", "x": 4460.71579866762, "y": 3901.3596814700904, "width": 108.89588928222656, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4j", "roundness": null, "seed": 1976820494, "version": 1080, "versionNonce": 700411945, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "make-an-audio", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "cD4ZjOJycVzD2FIgSHGKQ", "originalText": "make-an-audio", "autoResize": true, "lineHeight": 1.25}, {"id": "1CA4_U8s_Z7Ew48LF9QiF", "type": "text", "x": 4032.736190031283, "y": 3961.8371898150544, "width": 106.47990417480469, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4k", "roundness": null, "seed": 264261966, "version": 814, "versionNonce": 1586594569, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Requires \npytorch 2.6.0", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "Requires \npytorch 2.6.0", "autoResize": true, "lineHeight": 1.25}, {"id": "Mc0zIIQDX7khel_-ti6ao", "type": "text", "x": 4248.657686976776, "y": 3961.4055284601163, "width": 105.96791076660156, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4l", "roundness": null, "seed": 109377422, "version": 914, "versionNonce": 425605609, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Requires \npytorch 2.3.0", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "Requires \npytorch 2.3.0", "autoResize": true, "lineHeight": 1.25}, {"id": "MMvUKxBLFCcaQvGxMTRCP", "type": "text", "x": 4466.984520791019, "y": 3960.61540470205, "width": 107.76722626316108, "height": 40.12185988308511, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4m", "roundness": null, "seed": 39507406, "version": 1001, "versionNonce": 187765961, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Requires \npytorch 2.2.0", "fontSize": 16.048743953234045, "fontFamily": 5, "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "Requires \npytorch 2.2.0", "autoResize": true, "lineHeight": 1.25}, {"id": "k0FEmmFZ7gJSXgpCcDOk3", "type": "rectangle", "x": 3997.652336594729, "y": 4034.08044566458, "width": 180.5531061282278, "height": 70, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4u", "roundness": {"type": 3}, "seed": 720218062, "version": 1055, "versionNonce": 933598121, "isDeleted": false, "boundElements": [{"type": "text", "id": "E2R5r0zMy7xmuN6wgSjwn"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "E2R5r0zMy7xmuN6wgSjwn", "type": "text", "x": 4041.3769182232963, "y": 4049.08044566458, "width": 93.10394287109375, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4v", "roundness": null, "seed": 784563726, "version": 879, "versionNonce": 585578121, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Isolated \nEnvironment", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "k0FEmmFZ7gJSXgpCcDOk3", "originalText": "Isolated \nEnvironment", "autoResize": true, "lineHeight": 1.25}, {"id": "tFsx13EsKtd_VnvvV5dl-", "type": "freedraw", "x": 4157.335200211326, "y": 3994.383509149031, "width": 26.526144872815166, "height": 27.736881775724214, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4w", "roundness": null, "seed": 1124063758, "version": 600, "versionNonce": 1068596585, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [0.34592482940206537, 0.11399795514353173], [0.9434313529154679, 0.6289542352769786], [1.3679754617278377, 1.3286658220226855], [1.7060383631892364, 2.0952037962670147], [2.154168255824061, 2.8696036984520106], [2.755605743308024, 3.6597274565187945], [3.2233904557951973, 4.170752772681681], [3.376698050644336, 4.331922295471486], [3.3806290146148967, 4.587434953552929], [3.3806290146148967, 4.355508079294395], [3.3806290146148967, 3.7658634837221143], [3.3806290146148967, 3.0622209330058467], [3.3806290146148967, 1.926172345536088], [3.3806290146148967, 0.5739207396900383], [3.55752239328649, -1.0770841279122578], [4.076409637390043, -2.7595367072790395], [4.6110207373758385, -4.430196394733684], [5.145631837361179, -6.073339334395314], [6.568640794675957, -9.304591718132087], [7.252628525539876, -10.680429107801046], [9.05301002402075, -13.54217087831239], [9.583690160035985, -14.513118979021328], [10.216575359283524, -15.275725989295097], [11.026353937203112, -16.40784361279384], [11.851856371004033, -17.610718587761312], [12.65770398495306, -18.510909337001976], [14.658564645928891, -20.519631925918475], [15.377931052526947, -21.0267262781108], [16.407843612793386, -21.61243990971252], [17.69719979511183, -22.182429685432453], [19.092692004633136, -22.575526082480792], [20.307359871511835, -22.93324380379454], [21.518096774420428, -23.117999110407254], [22.717040785417794, -23.137653930259603], [23.707643705979535, -23.145515858200724], [24.49383650007576, -23.149446822171285], [25.071688203736812, -23.149446822171285], [25.64560894342685, -23.149446822171285], [26.0622911242981, -23.149446822171285], [26.526144872815166, -23.149446822171285], [26.526144872815166, -23.149446822171285]], "pressures": [], "simulatePressure": true, "lastCommittedPoint": null}, {"id": "jzQdAX387QmWOa3LV8ENc", "type": "freedraw", "x": 4156.780934291488, "y": 3999.010253742288, "width": 7.681103598322807, "height": 8.19999084242636, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b4x", "roundness": null, "seed": 2076047822, "version": 594, "versionNonce": 491776073, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [0, -0.11792891911454717], [0, -0.3302009735207321], [0, -0.5542659198381443], [0, -0.817640505860254], [0, -1.3561825698166103], [-0.37737254116655095, -1.8043124624514348], [-0.6564709830709035, -2.0952037962670147], [-0.9316384610046953, -2.2485113911161534], [-1.195013047026805, -2.5079550131677024], [-1.3247348580525795, -2.7634676712491455], [-2.0362393367099685, -3.030773221241816], [-2.299613922732533, -3.2941478072643804], [-2.5079550131677024, -3.5024888977000046], [-2.6416077881644924, -3.7658634837221143], [-2.7791915271313883, -4.033169033714785], [-3.042566113153498, -4.166821808711575], [-3.357043230792442, -4.6110207373758385], [-3.4906960057887773, -4.8547405035455995], [-3.6086249249028697, -4.968738458690041], [-3.860206619014207, -5.0945293057452545], [-4.280819763855561, -5.365765819708486], [-4.7014329086969155, -5.6409332976422775], [-5.263560756476181, -6.120510902041133], [-5.593761729996913, -6.438918983650183], [-5.916100775576524, -6.832015380698522], [-6.328851992476757, -7.146492498337011], [-7.032494543193025, -7.614277210824639], [-7.3194549130384985, -7.775446733614444], [-7.681103598322807, -8.19999084242636], [-7.681103598322807, -8.19999084242636]], "pressures": [], "simulatePressure": true, "lastCommittedPoint": null}, {"id": "rviE1y9PzdWFG-7L5ydnh", "type": "freedraw", "x": 4381.142668388758, "y": 3997.7248285239402, "width": 26.526144872815166, "height": 27.736881775724214, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b50", "roundness": null, "seed": 1242664910, "version": 639, "versionNonce": 1436415785, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [0.34592482940206537, 0.11399795514353173], [0.9434313529154679, 0.6289542352769786], [1.3679754617278377, 1.3286658220226855], [1.7060383631892364, 2.0952037962670147], [2.154168255824061, 2.8696036984520106], [2.755605743308024, 3.6597274565187945], [3.2233904557951973, 4.170752772681681], [3.376698050644336, 4.331922295471486], [3.3806290146148967, 4.587434953552929], [3.3806290146148967, 4.355508079294395], [3.3806290146148967, 3.7658634837221143], [3.3806290146148967, 3.0622209330058467], [3.3806290146148967, 1.926172345536088], [3.3806290146148967, 0.5739207396900383], [3.55752239328649, -1.0770841279122578], [4.076409637390043, -2.7595367072790395], [4.6110207373758385, -4.430196394733684], [5.145631837361179, -6.073339334395314], [6.568640794675957, -9.304591718132087], [7.252628525539876, -10.680429107801046], [9.05301002402075, -13.54217087831239], [9.583690160035985, -14.513118979021328], [10.216575359283524, -15.275725989295097], [11.026353937203112, -16.40784361279384], [11.851856371004033, -17.610718587761312], [12.65770398495306, -18.510909337001976], [14.658564645928891, -20.519631925918475], [15.377931052526947, -21.0267262781108], [16.407843612793386, -21.61243990971252], [17.69719979511183, -22.182429685432453], [19.092692004633136, -22.575526082480792], [20.307359871511835, -22.93324380379454], [21.518096774420428, -23.117999110407254], [22.717040785417794, -23.137653930259603], [23.707643705979535, -23.145515858200724], [24.49383650007576, -23.149446822171285], [25.071688203736812, -23.149446822171285], [25.64560894342685, -23.149446822171285], [26.0622911242981, -23.149446822171285], [26.526144872815166, -23.149446822171285], [26.526144872815166, -23.149446822171285]], "pressures": [], "simulatePressure": true, "lastCommittedPoint": null}, {"id": "7tjepOsAQQdKvGMrUpAkG", "type": "freedraw", "x": 4380.588402468919, "y": 4002.3515731171974, "width": 7.681103598322807, "height": 8.19999084242636, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b51", "roundness": null, "seed": 287991310, "version": 633, "versionNonce": 990325257, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [0, -0.11792891911454717], [0, -0.3302009735207321], [0, -0.5542659198381443], [0, -0.817640505860254], [0, -1.3561825698166103], [-0.37737254116655095, -1.8043124624514348], [-0.6564709830709035, -2.0952037962670147], [-0.9316384610046953, -2.2485113911161534], [-1.195013047026805, -2.5079550131677024], [-1.3247348580525795, -2.7634676712491455], [-2.0362393367099685, -3.030773221241816], [-2.299613922732533, -3.2941478072643804], [-2.5079550131677024, -3.5024888977000046], [-2.6416077881644924, -3.7658634837221143], [-2.7791915271313883, -4.033169033714785], [-3.042566113153498, -4.166821808711575], [-3.357043230792442, -4.6110207373758385], [-3.4906960057887773, -4.8547405035455995], [-3.6086249249028697, -4.968738458690041], [-3.860206619014207, -5.0945293057452545], [-4.280819763855561, -5.365765819708486], [-4.7014329086969155, -5.6409332976422775], [-5.263560756476181, -6.120510902041133], [-5.593761729996913, -6.438918983650183], [-5.916100775576524, -6.832015380698522], [-6.328851992476757, -7.146492498337011], [-7.032494543193025, -7.614277210824639], [-7.3194549130384985, -7.775446733614444], [-7.681103598322807, -8.19999084242636], [-7.681103598322807, -8.19999084242636]], "pressures": [], "simulatePressure": true, "lastCommittedPoint": null}, {"id": "mfRdQ7oBR-m05Knc4MBNq", "type": "rectangle", "x": 4210.656760988935, "y": 4031.587455358852, "width": 180.5531061282278, "height": 70, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b54", "roundness": {"type": 3}, "seed": 427802642, "version": 1164, "versionNonce": 1183252713, "isDeleted": false, "boundElements": [{"type": "text", "id": "Vl2mFkmnnVMNogdiWuLtr"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "Vl2mFkmnnVMNogdiWuLtr", "type": "text", "x": 4254.381342617502, "y": 4046.587455358852, "width": 93.10394287109375, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b55", "roundness": null, "seed": 393342418, "version": 989, "versionNonce": 1168702409, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Isolated \nEnvironment", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "mfRdQ7oBR-m05Knc4MBNq", "originalText": "Isolated \nEnvironment", "autoResize": true, "lineHeight": 1.25}, {"id": "Rl_MKwzMmtfk6LvrFmPYi", "type": "rectangle", "x": 4427.268599618398, "y": 4029.881416995662, "width": 180.5531061282278, "height": 70, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b56", "roundness": {"type": 3}, "seed": 1633991822, "version": 1289, "versionNonce": 1216912041, "isDeleted": false, "boundElements": [{"type": "text", "id": "K-F1nZAIoLfkIbflu-rXg"}], "updated": 1742298167955, "link": null, "locked": false}, {"id": "K-F1nZAIoLfkIbflu-rXg", "type": "text", "x": 4470.993181246965, "y": 4044.881416995662, "width": 93.10394287109375, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b57", "roundness": null, "seed": 1459223246, "version": 1114, "versionNonce": 1339121033, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Isolated \nEnvironment", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "Rl_MKwzMmtfk6LvrFmPYi", "originalText": "Isolated \nEnvironment", "autoResize": true, "lineHeight": 1.25}, {"id": "E5hyFM8SIV52FsG9pgPW9", "type": "arrow", "x": 3863.142519156638, "y": 3989.126019994088, "width": 121.93413594001186, "height": 0.5890261702379576, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b58", "roundness": null, "seed": 974919442, "version": 565, "versionNonce": 985580649, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [121.93413594001186, -0.5890261702379576]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "TdEGdXpFM0CcpUrwJd2gF", "type": "freedraw", "x": 4598.8692340585685, "y": 3999.3330418164333, "width": 26.526144872815166, "height": 27.736881775724214, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b59", "roundness": null, "seed": 1161807758, "version": 760, "versionNonce": 1964789577, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [0.34592482940206537, 0.11399795514353173], [0.9434313529154679, 0.6289542352769786], [1.3679754617278377, 1.3286658220226855], [1.7060383631892364, 2.0952037962670147], [2.154168255824061, 2.8696036984520106], [2.755605743308024, 3.6597274565187945], [3.2233904557951973, 4.170752772681681], [3.376698050644336, 4.331922295471486], [3.3806290146148967, 4.587434953552929], [3.3806290146148967, 4.355508079294395], [3.3806290146148967, 3.7658634837221143], [3.3806290146148967, 3.0622209330058467], [3.3806290146148967, 1.926172345536088], [3.3806290146148967, 0.5739207396900383], [3.55752239328649, -1.0770841279122578], [4.076409637390043, -2.7595367072790395], [4.6110207373758385, -4.430196394733684], [5.145631837361179, -6.073339334395314], [6.568640794675957, -9.304591718132087], [7.252628525539876, -10.680429107801046], [9.05301002402075, -13.54217087831239], [9.583690160035985, -14.513118979021328], [10.216575359283524, -15.275725989295097], [11.026353937203112, -16.40784361279384], [11.851856371004033, -17.610718587761312], [12.65770398495306, -18.510909337001976], [14.658564645928891, -20.519631925918475], [15.377931052526947, -21.0267262781108], [16.407843612793386, -21.61243990971252], [17.69719979511183, -22.182429685432453], [19.092692004633136, -22.575526082480792], [20.307359871511835, -22.93324380379454], [21.518096774420428, -23.117999110407254], [22.717040785417794, -23.137653930259603], [23.707643705979535, -23.145515858200724], [24.49383650007576, -23.149446822171285], [25.071688203736812, -23.149446822171285], [25.64560894342685, -23.149446822171285], [26.0622911242981, -23.149446822171285], [26.526144872815166, -23.149446822171285], [26.526144872815166, -23.149446822171285]], "pressures": [], "simulatePressure": true, "lastCommittedPoint": null}, {"id": "sgC8nBUnszBAK-6SUrXfF", "type": "freedraw", "x": 4598.314968138732, "y": 4003.9597864096904, "width": 7.681103598322807, "height": 8.19999084242636, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5A", "roundness": null, "seed": 1094428110, "version": 754, "versionNonce": 1699053097, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "points": [[0, 0], [0, -0.11792891911454717], [0, -0.3302009735207321], [0, -0.5542659198381443], [0, -0.817640505860254], [0, -1.3561825698166103], [-0.37737254116655095, -1.8043124624514348], [-0.6564709830709035, -2.0952037962670147], [-0.9316384610046953, -2.2485113911161534], [-1.195013047026805, -2.5079550131677024], [-1.3247348580525795, -2.7634676712491455], [-2.0362393367099685, -3.030773221241816], [-2.299613922732533, -3.2941478072643804], [-2.5079550131677024, -3.5024888977000046], [-2.6416077881644924, -3.7658634837221143], [-2.7791915271313883, -4.033169033714785], [-3.042566113153498, -4.166821808711575], [-3.357043230792442, -4.6110207373758385], [-3.4906960057887773, -4.8547405035455995], [-3.6086249249028697, -4.968738458690041], [-3.860206619014207, -5.0945293057452545], [-4.280819763855561, -5.365765819708486], [-4.7014329086969155, -5.6409332976422775], [-5.263560756476181, -6.120510902041133], [-5.593761729996913, -6.438918983650183], [-5.916100775576524, -6.832015380698522], [-6.328851992476757, -7.146492498337011], [-7.032494543193025, -7.614277210824639], [-7.3194549130384985, -7.775446733614444], [-7.681103598322807, -8.19999084242636], [-7.681103598322807, -8.19999084242636]], "pressures": [], "simulatePressure": true, "lastCommittedPoint": null}, {"id": "CDHKj817ZeuVKbAZ1JzY5", "type": "text", "x": 3223.829260323124, "y": 3821.653714952782, "width": 568.279541015625, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5B", "roundness": null, "seed": 878180558, "version": 1266, "versionNonce": 438685961, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Problem: Same package is needed, but in different versions", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Problem: Same package is needed, but in different versions", "autoResize": true, "lineHeight": 1.25}, {"id": "Tf5uvHwKdEQ_9KwPoQakk", "type": "text", "x": 3225.973604587867, "y": 4175.892406244889, "width": 550.719482421875, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5C", "roundness": null, "seed": 744982158, "version": 1219, "versionNonce": 960022505, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Base image\n-Specified in a docker image\n-Pre-installs drivers, dependencies, PyTorch versions etc", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Base image\n-Specified in a docker image\n-Pre-installs drivers, dependencies, PyTorch versions etc", "autoResize": true, "lineHeight": 1.25}, {"id": "mc8LZRx051QJtsofUvl0n", "type": "text", "x": 3221.0518157120187, "y": 3719.864219622279, "width": 303.7998046875, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5D", "roundness": null, "seed": 2140136850, "version": 1037, "versionNonce": 747233993, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Virtual computer on a computer\n-Operating system is linux", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Virtual computer on a computer\n-Operating system is linux", "autoResize": true, "lineHeight": 1.25}, {"id": "8zoHcJHO_ShLBtynymizA", "type": "text", "x": 3230.118494559542, "y": 4288.091208550977, "width": 396.7597351074219, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5E", "roundness": null, "seed": 2003538, "version": 1392, "versionNonce": 280273321, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "List of dependencies in a file\n-Will be automatically installed when built", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "List of dependencies in a file\n-Will be automatically installed when built", "autoResize": true, "lineHeight": 1.25}, {"id": "735n3Qfgk9GUXWBUfhXj8", "type": "text", "x": 3230.3503160081314, "y": 4511.02709422992, "width": 543.799560546875, "height": 125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5F", "roundness": null, "seed": 894048334, "version": 1794, "versionNonce": 1659143305, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Isolated resources and processes\n-You can specify what resources the container should\nhave available\n-If you mess up a container badly and break everything,\nyou cna just create a fresh new instance", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Isolated resources and processes\n-You can specify what resources the container should\nhave available\n-If you mess up a container badly and break everything,\nyou cna just create a fresh new instance", "autoResize": true, "lineHeight": 1.25}, {"id": "Cba236jn1lrwTrR0jYPn0", "type": "text", "x": 3229.729674913344, "y": 4670.627255627157, "width": 521.939697265625, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5G", "roundness": null, "seed": 321308366, "version": 2048, "versionNonce": 1859455849, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Scaling\n-If you need twice the computing power, just put the\nsame container on another machine,", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Scaling\n-If you need twice the computing power, just put the\nsame container on another machine,", "autoResize": true, "lineHeight": 1.25}, {"id": "XzXT7QISTyXC7qWx3t3fu", "type": "text", "x": 3236.041819413149, "y": 4792.910956331941, "width": 516.2396240234375, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5H", "roundness": null, "seed": 199532174, "version": 2302, "versionNonce": 2094118473, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "We can define how a training job should work on our\nlocal machine, and then send it to a computer in the \ncloud with a powerful GPU, knowing that it'll work.", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "We can define how a training job should work on our\nlocal machine, and then send it to a computer in the \ncloud with a powerful GPU, knowing that it'll work.", "autoResize": true, "lineHeight": 1.25}, {"id": "nS29JK-H4L4JQQDutmMwf", "type": "text", "x": -807.1651334605447, "y": 1691.7947138059492, "width": 255.1798553466797, "height": 125, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5J", "roundness": null, "seed": 1565443730, "version": 980, "versionNonce": 1068893481, "isDeleted": false, "boundElements": [], "updated": 1742298167955, "link": null, "locked": false, "text": "Find model on GitHub\n-Stars (popularity)\n-How does it perform?\n-Can it be fine-tuned?\n-Is it hard to get to run?", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Find model on GitHub\n-Stars (popularity)\n-How does it perform?\n-Can it be fine-tuned?\n-Is it hard to get to run?", "autoResize": true, "lineHeight": 1.25}, {"id": "QYPQAYmNj1OU1QOB8Am6E", "type": "text", "x": -806.6157014635892, "y": 1476.7693636262575, "width": 468.9997253417969, "height": 150, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5Q", "roundness": null, "seed": 263595858, "version": 1415, "versionNonce": 49117193, "isDeleted": false, "boundElements": [], "updated": 1742298167956, "link": null, "locked": false, "text": "Self-hosting\n+Removing dependence on external API's\n-Updates to model etc. have to happen manually\n\n++This is what AI API's do themselves, so\nyou'll learn a ton by trying to self-host AI ", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Self-hosting\n+Removing dependence on external API's\n-Updates to model etc. have to happen manually\n\n++This is what AI API's do themselves, so\nyou'll learn a ton by trying to self-host AI ", "autoResize": true, "lineHeight": 1.25}, {"id": "7SjLta9J0BPxYl-GRndYm", "type": "rectangle", "x": 3167.497060095619, "y": 1039.1347122593102, "width": 252.61233207268288, "height": 72.74527180512678, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5R", "roundness": {"type": 3}, "seed": 1416375890, "version": 249, "versionNonce": 410226409, "isDeleted": false, "boundElements": [{"type": "text", "id": "Iwk7cLtwTU8_-SHeWZMF3"}, {"id": "jrV4aroW_uaUmCK_Ucj_v", "type": "arrow"}], "updated": 1742298167956, "link": null, "locked": false}, {"id": "Iwk7cLtwTU8_-SHeWZMF3", "type": "text", "x": 3204.843288082644, "y": 1063.0073481618736, "width": 177.9198760986328, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5S", "roundness": null, "seed": 1059536274, "version": 275, "versionNonce": 114543049, "isDeleted": false, "boundElements": [], "updated": 1742298167956, "link": null, "locked": false, "text": "Existing model file", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "7SjLta9J0BPxYl-GRndYm", "originalText": "Existing model file", "autoResize": true, "lineHeight": 1.25}, {"id": "h_p98p3GrIkaDzxOUUOmi", "type": "rectangle", "x": 3626.9828108655006, "y": 1040.7414407552492, "width": 252.61233207268288, "height": 72.74527180512678, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5T", "roundness": {"type": 3}, "seed": 259075470, "version": 433, "versionNonce": 1999218857, "isDeleted": false, "boundElements": [{"type": "text", "id": "yhElkSD8lwIA5wzPIV2Th"}, {"id": "jrV4aroW_uaUmCK_Ucj_v", "type": "arrow"}], "updated": 1742298167956, "link": null, "locked": false}, {"id": "yhElkSD8lwIA5wzPIV2Th", "type": "text", "x": 3662.529035800768, "y": 1064.6140766578126, "width": 181.51988220214844, "height": 25, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5U", "roundness": null, "seed": 1280330702, "version": 455, "versionNonce": 1663085449, "isDeleted": false, "boundElements": [], "updated": 1742298167956, "link": null, "locked": false, "text": "Updated model file", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "h_p98p3GrIkaDzxOUUOmi", "originalText": "Updated model file", "autoResize": true, "lineHeight": 1.25}, {"id": "jrV4aroW_uaUmCK_Ucj_v", "type": "arrow", "x": 3421.057026759969, "y": 1077.8092231089745, "width": 203.70547018642037, "height": 1.3681755048951345, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5V", "roundness": null, "seed": 1521984142, "version": 629, "versionNonce": 1866487943, "isDeleted": false, "boundElements": [], "updated": 1742298168022, "link": null, "locked": false, "points": [[0, 0], [203.70547018642037, -1.3681755048951345]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "rQ0eRwEzqB6Qwy4Xx5vlx", "type": "text", "x": 3472.5391523857916, "y": 1029.2913538080231, "width": 101.11990356445312, "height": 40, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5W", "roundness": null, "seed": 1124962894, "version": 316, "versionNonce": 2070945097, "isDeleted": false, "boundElements": [], "updated": 1742298167956, "link": null, "locked": false, "text": "Fine-tune on\nspecific data", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "Fine-tune on\nspecific data", "autoResize": true, "lineHeight": 1.25}, {"id": "WYnGwsyk1R-IaSWDtMd-l", "type": "text", "x": 3169.7073899947754, "y": 1126.1851775521495, "width": 158.86386108398438, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5X", "roundness": null, "seed": 1839696658, "version": 223, "versionNonce": 1273990185, "isDeleted": false, "boundElements": [], "updated": 1742298167956, "link": null, "locked": false, "text": "Made by researchers", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "Made by researchers", "autoResize": true, "lineHeight": 1.25}, {"id": "RhkWeZAAhBK8pk4wkZIWW", "type": "text", "x": 3635.274252646997, "y": 1125.0546335089248, "width": 203.83981323242188, "height": 20, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5Y", "roundness": null, "seed": 1644947918, "version": 357, "versionNonce": 315731879, "isDeleted": false, "boundElements": [], "updated": 1742298168022, "link": null, "locked": false, "text": "Modified to your use-case", "fontSize": 16, "fontFamily": 5, "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "Modified to your use-case", "autoResize": true, "lineHeight": 1.25}, {"id": "Ed5acWx7QqvgNLJoOj9Fs", "type": "arrow", "x": 7092.695480718308, "y": 1422.701154026181, "width": 0, "height": 60.6934720821946, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5Z", "roundness": null, "seed": 906351287, "version": 546, "versionNonce": 461811913, "isDeleted": false, "boundElements": [], "updated": 1742298167956, "link": null, "locked": false, "points": [[0, 0], [0, 60.6934720821946]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "XMEwFZysijRoiHBMqLl-A", "type": "arrow", "x": 7443.608328186617, "y": 1561.7638289266297, "width": 1.9367500696453135, "height": 64.39208174696887, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5a", "roundness": null, "seed": 317591865, "version": 928, "versionNonce": 1659350953, "isDeleted": false, "boundElements": [], "updated": 1742298167956, "link": null, "locked": false, "points": [[0, 0], [-1.9367500696453135, 64.39208174696887]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "QeQxW7D_SWA_f3HySkqDS", "type": "arrow", "x": 7339.833425599981, "y": 1801.7272685304245, "width": 138.3400562566576, "height": 1.7947121091424378, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5b", "roundness": null, "seed": 1317394137, "version": 1136, "versionNonce": 615068135, "isDeleted": false, "boundElements": [], "updated": 1742298168022, "link": null, "locked": false, "points": [[0, 0], [-138.3400562566576, 1.7947121091424378]], "lastCommittedPoint": null, "startBinding": {"elementId": "nfh0aCh1H36DJY0oIqhza", "focus": 0.010698875497459385, "gap": 1.955742141391056}, "endBinding": {"elementId": "VpOEX1Iz2QygI7zjq1kcO", "focus": 0.13748122582336958, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "3XIKmHz4erMfTNcnIKJMg", "type": "arrow", "x": 7445.365297399261, "y": 1697.8206192357989, "width": 1.9367500696453135, "height": 64.39208174696887, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5c", "roundness": null, "seed": 1370909527, "version": 1007, "versionNonce": 505190761, "isDeleted": false, "boundElements": [], "updated": 1742298167956, "link": null, "locked": false, "points": [[0, 0], [-1.9367500696453135, 64.39208174696887]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "rOeWveCDplT-NtzjHwlUL", "type": "rectangle", "x": 10044.840151234834, "y": 1927.5933917444615, "width": 277.78916537329224, "height": 101.85682924688672, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5d", "roundness": {"type": 3}, "seed": 996840663, "version": 640, "versionNonce": 938968137, "isDeleted": false, "boundElements": [{"type": "text", "id": "I-QtE2dOPWqQzncwUFtBD"}], "updated": 1742298167956, "link": null, "locked": false}, {"id": "I-QtE2dOPWqQzncwUFtBD", "type": "text", "x": 10119.364776951265, "y": 1966.021806367905, "width": 128.7399139404297, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5e", "roundness": null, "seed": 1024451063, "version": 670, "versionNonce": 1894912809, "isDeleted": false, "boundElements": [], "updated": 1742298167956, "link": null, "locked": false, "text": "Next.js client", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "rOeWveCDplT-NtzjHwlUL", "originalText": "Next.js client", "autoResize": true, "lineHeight": 1.25}, {"id": "9GjVoaM4rKfyXuQfrytLn", "type": "rectangle", "x": 10383.970845213606, "y": 1928.1452849015868, "width": 277.78916537329224, "height": 101.85682924688672, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5f", "roundness": {"type": 3}, "seed": 78328057, "version": 675, "versionNonce": 225953289, "isDeleted": false, "boundElements": [{"type": "text", "id": "pQpIg6wP8jT376C_-5ZnS"}], "updated": 1742298167956, "link": null, "locked": false}, {"id": "pQpIg6wP8jT376C_-5ZnS", "type": "text", "x": 10445.31548588365, "y": 1966.5736995250302, "width": 155.09988403320312, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5g", "roundness": null, "seed": 1038920153, "version": 745, "versionNonce": 948182249, "isDeleted": false, "boundElements": [], "updated": 1742298167956, "link": null, "locked": false, "text": "Node.js backend", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "9GjVoaM4rKfyXuQfrytLn", "originalText": "Node.js backend", "autoResize": true, "lineHeight": 1.25}, {"id": "yJf2zumHJsdtSeKyfIiKL", "type": "rectangle", "x": 10734.495466940181, "y": 1929.4585159619264, "width": 277.78916537329224, "height": 101.85682924688672, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5h", "roundness": {"type": 3}, "seed": 1548764855, "version": 879, "versionNonce": 832739273, "isDeleted": false, "boundElements": [{"type": "text", "id": "z_KwyYfxy1lBzpO7_kzf8"}], "updated": 1742298167956, "link": null, "locked": false}, {"id": "z_KwyYfxy1lBzpO7_kzf8", "type": "text", "x": 10756.570126225948, "y": 1967.8869305853698, "width": 233.6398468017578, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5i", "roundness": null, "seed": 2116310999, "version": 976, "versionNonce": 988722857, "isDeleted": false, "boundElements": [], "updated": 1742298167956, "link": null, "locked": false, "text": "Our api backend on EC2", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "yJf2zumHJsdtSeKyfIiKL", "originalText": "Our api backend on EC2", "autoResize": true, "lineHeight": 1.25}, {"id": "D0mILZJXG0BR6Usgnc643", "type": "rectangle", "x": 10007.267263381867, "y": 1884.0953274253777, "width": 688.951798223994, "height": 186.30524137566817, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5j", "roundness": {"type": 3}, "seed": 463016055, "version": 110, "versionNonce": 1192491401, "isDeleted": false, "boundElements": [], "updated": 1742298167956, "link": null, "locked": false}, {"id": "O_FlecM-E8EoR3FkkbMPo", "type": "text", "x": 10030.84647444033, "y": 1853.3015327611122, "width": 68.9599609375, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b5k", "roundness": null, "seed": 1521144473, "version": 18, "versionNonce": 1733156969, "isDeleted": false, "boundElements": [], "updated": 1742298167956, "link": null, "locked": false, "text": "Next.js", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Next.js", "autoResize": true, "lineHeight": 1.25}, {"id": "j0cb_JK7COT7St7Yxo3YT", "type": "rectangle", "x": 11723.90983159632, "y": 1820.7074635328227, "width": 1194.5244462075339, "height": 454.7218198083346, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b64", "roundness": {"type": 3}, "seed": 1358751559, "version": 1096, "versionNonce": 326192489, "isDeleted": false, "boundElements": [], "updated": 1742299634736, "link": null, "locked": false}, {"id": "xV-EVo4SFUjlIN6rxvgRE", "type": "rectangle", "x": 13003.487590048362, "y": 1909.783671852275, "width": 204.94961940606152, "height": 78.74723715759697, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b65", "roundness": {"type": 3}, "seed": 147258311, "version": 1940, "versionNonce": 851100615, "isDeleted": false, "boundElements": [{"type": "text", "id": "J2me4WoxwL4qzak9PKapW"}, {"id": "DoO0J8xhWAgUDS4MCDJr-", "type": "arrow"}], "updated": 1742299517072, "link": null, "locked": false}, {"id": "J2me4WoxwL4qzak9PKapW", "type": "text", "x": 13033.80247238323, "y": 1924.1486867407586, "width": 144.31985473632812, "height": 50.01720738062966, "angle": 0, "strokeColor": "#f08c00", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b66", "roundness": null, "seed": 1646389991, "version": 1922, "versionNonce": 161931687, "isDeleted": false, "boundElements": [], "updated": 1742299735937, "link": null, "locked": false, "text": "EC2 instance\nw. model's api's", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "xV-EVo4SFUjlIN6rxvgRE", "originalText": "EC2 instance\nw. model's api's", "autoResize": true, "lineHeight": 1.25}, {"id": "qHBY0YzhCBNuVQs92Afmn", "type": "text", "x": 11543.113591267134, "y": 902.2524019242479, "width": 136.25994873046875, "height": 45, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b67", "roundness": null, "seed": 1269079849, "version": 2888, "versionNonce": 2096735601, "isDeleted": false, "boundElements": [], "updated": 1742589897626, "link": null, "locked": false, "text": "Inngest", "fontSize": 36, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Inngest", "autoResize": true, "lineHeight": 1.25}, {"id": "5JSllvD5kAkh2oWazeiSd", "type": "text", "x": 11725.043297736387, "y": 1779.4899063647294, "width": 137.8599090576172, "height": 25, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b68", "roundness": null, "seed": 539162503, "version": 579, "versionNonce": 287529383, "isDeleted": false, "boundElements": [], "updated": 1742299634736, "link": null, "locked": false, "text": "Next.js Server", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Next.js Server", "autoResize": true, "lineHeight": 1.25}, {"id": "0FKmN6b0zf7DbAaJtcNcn", "type": "rectangle", "x": 11730.964028848968, "y": 2494.038085750394, "width": 952.5763346191299, "height": 276.36663871790205, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6D", "roundness": {"type": 3}, "seed": 908264489, "version": 1533, "versionNonce": 102259687, "isDeleted": false, "boundElements": [], "updated": 1742299639581, "link": null, "locked": false}, {"id": "d2KFSFuZQhI28AiE8uD3X", "type": "text", "x": 11731.39498098924, "y": 2453.7310281113355, "width": 131.2399139404297, "height": 25, "angle": 0, "strokeColor": "#e03131", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6E", "roundness": null, "seed": 1096980233, "version": 777, "versionNonce": 1259928073, "isDeleted": false, "boundElements": [], "updated": 1742299639581, "link": null, "locked": false, "text": "Next.js Client", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Next.js Client", "autoResize": true, "lineHeight": 1.25}, {"id": "Xixc04NnN25Bf9B7NJC1r", "type": "text", "x": 12572.775112326519, "y": 1558.853671447854, "width": 330.4797668457031, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6G", "roundness": null, "seed": 661874599, "version": 1094, "versionNonce": 2112934985, "isDeleted": false, "boundElements": [], "updated": 1742299909185, "link": null, "locked": false, "text": "Inngest queue (throttling applies)", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Inngest queue (throttling applies)", "autoResize": true, "lineHeight": 1.25}, {"id": "gzMs9qfoAbZkkqQ8ZuWr1", "type": "rectangle", "x": 12565.430463532512, "y": 1610.060614764522, "width": 451.62971207709654, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6K", "roundness": {"type": 3}, "seed": 1000156743, "version": 1966, "versionNonce": 1918441895, "isDeleted": false, "boundElements": [{"id": "QCNwp41vDWJHnGSr3Cu__", "type": "arrow"}, {"id": "ZJNa9mvnTsPaBu8MS-jTO", "type": "arrow"}], "updated": 1742299517072, "link": null, "locked": false}, {"id": "-QvTgKsTC18qbRz0qrDX1", "type": "rectangle", "x": 12220.435294046109, "y": 1603.4875347844975, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6M", "roundness": {"type": 3}, "seed": 1312932295, "version": 2144, "versionNonce": 1245147007, "isDeleted": false, "boundElements": [{"type": "text", "id": "3VvISBTxi1FM84urHrXh2"}, {"id": "mO1bQlZ03vCyc8PDhsyog", "type": "arrow"}, {"id": "8ggeEqIRwp_XDtBvmiq33", "type": "arrow"}, {"id": "MfrdcTFxN9lzZWoXfe4Cy", "type": "arrow"}], "updated": 1742575205710, "link": null, "locked": false}, {"id": "3VvISBTxi1FM84urHrXh2", "type": "text", "x": 12275.866828341419, "y": 1630.3568515181387, "width": 93.919921875, "height": 25.00860369031483, "angle": 0, "strokeColor": "#2f9e44", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6N", "roundness": null, "seed": 2018767079, "version": 2090, "versionNonce": 1975199647, "isDeleted": false, "boundElements": [], "updated": 1742575205710, "link": null, "locked": false, "text": "Database", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "-QvTgKsTC18qbRz0qrDX1", "originalText": "Database", "autoResize": true, "lineHeight": 1.25}, {"id": "6GP9_K-UZREfTIyfe8_YZ", "type": "line", "x": 12616.248707602326, "y": 1610.9773568952126, "width": 0.40111150558368536, "height": 76.76164759537915, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6O", "roundness": {"type": 2}, "seed": 18353799, "version": 571, "versionNonce": 654930023, "isDeleted": false, "boundElements": [], "updated": 1742299517073, "link": null, "locked": false, "points": [[0, 0], [0.40111150558368536, 76.76164759537915]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "yytzgDQ04Ndgc1Qb1h6VA", "type": "line", "x": 12665.798700569176, "y": 1612.0069522588615, "width": 0.40111150558368536, "height": 76.76164759537915, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6P", "roundness": {"type": 2}, "seed": 2133193383, "version": 585, "versionNonce": 1164757895, "isDeleted": false, "boundElements": [], "updated": 1742299517073, "link": null, "locked": false, "points": [[0, 0], [0.40111150558368536, 76.76164759537915]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "kfVvaMGcXEdnRE6DRsve6", "type": "line", "x": 12714.781672765592, "y": 1611.7367974502854, "width": 0.40111150558368536, "height": 76.76164759537915, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6Q", "roundness": {"type": 2}, "seed": 1734445961, "version": 596, "versionNonce": 1895285415, "isDeleted": false, "boundElements": [], "updated": 1742299517073, "link": null, "locked": false, "points": [[0, 0], [0.40111150558368536, 76.76164759537915]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "7enh5KVrwhJUXtMsLvQIK", "type": "line", "x": 12764.331665732443, "y": 1612.766392813934, "width": 0.40111150558368536, "height": 76.76164759537915, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6R", "roundness": {"type": 2}, "seed": 202977897, "version": 610, "versionNonce": 1515640263, "isDeleted": false, "boundElements": [], "updated": 1742299517073, "link": null, "locked": false, "points": [[0, 0], [0.40111150558368536, 76.76164759537915]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "Xa7nxLDmhxGsXBEjnOBmX", "type": "line", "x": 12817.457987463831, "y": 1610.4342655243408, "width": 0.40111150558368536, "height": 76.76164759537915, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6S", "roundness": {"type": 2}, "seed": 815091337, "version": 661, "versionNonce": 1755743463, "isDeleted": false, "boundElements": [], "updated": 1742299517073, "link": null, "locked": false, "points": [[0, 0], [0.40111150558368536, 76.76164759537915]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "oD84yH_v8cEi1n4RceCEi", "type": "line", "x": 12867.007980430682, "y": 1611.46386088799, "width": 0.40111150558368536, "height": 76.76164759537915, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6T", "roundness": {"type": 2}, "seed": 534549865, "version": 675, "versionNonce": 1802719239, "isDeleted": false, "boundElements": [], "updated": 1742299517073, "link": null, "locked": false, "points": [[0, 0], [0.40111150558368536, 76.76164759537915]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "e85oEKmu_aKRPOEjENY5n", "type": "line", "x": 12915.990952627099, "y": 1611.193706079414, "width": 0.40111150558368536, "height": 76.76164759537915, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6U", "roundness": {"type": 2}, "seed": 2019033161, "version": 686, "versionNonce": 213497639, "isDeleted": false, "boundElements": [], "updated": 1742299517073, "link": null, "locked": false, "points": [[0, 0], [0.40111150558368536, 76.76164759537915]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "2j39tAYIFOmEJMQN1krlx", "type": "line", "x": 12965.54094559395, "y": 1612.2233014430626, "width": 0.40111150558368536, "height": 76.76164759537915, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6V", "roundness": {"type": 2}, "seed": 1206062889, "version": 700, "versionNonce": 48642631, "isDeleted": false, "boundElements": [], "updated": 1742299517073, "link": null, "locked": false, "points": [[0, 0], [0.40111150558368536, 76.76164759537915]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "gW3_mgt_qawxb4tkw_wU4", "type": "rectangle", "x": 12593.838756187826, "y": 1884.0209451212, "width": 276.19044507424815, "height": 274.38206174154095, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6W", "roundness": {"type": 3}, "seed": 357474025, "version": 3049, "versionNonce": 47536487, "isDeleted": false, "boundElements": [{"id": "ZJNa9mvnTsPaBu8MS-jTO", "type": "arrow"}], "updated": 1742299517073, "link": null, "locked": false}, {"id": "IY0t59HxLJhJCeTYXFU4p", "type": "rectangle", "x": 11772.440654310627, "y": 2641.9171668422114, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6Y", "roundness": {"type": 3}, "seed": 1306123625, "version": 2249, "versionNonce": 527480711, "isDeleted": false, "boundElements": [{"type": "text", "id": "BOMII0o8Jt0nGD3zDJi2Q"}, {"id": "TGN2_6MJG4zgZMs8xp1kK", "type": "arrow"}], "updated": 1742299755368, "link": null, "locked": false}, {"id": "BOMII0o8Jt0nGD3zDJi2Q", "type": "text", "x": 11793.302204169902, "y": 2668.7864835758523, "width": 163.0598907470703, "height": 25.00860369031483, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6Z", "roundness": null, "seed": 124536905, "version": 2229, "versionNonce": 274685639, "isDeleted": false, "boundElements": [], "updated": 1742299517073, "link": null, "locked": false, "text": "Generate button", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "IY0t59HxLJhJCeTYXFU4p", "originalText": "Generate button", "autoResize": true, "lineHeight": 1.25}, {"id": "GGMg3bpfSLAFRFyJFOpH3", "type": "rectangle", "x": 11787.66605167234, "y": 1910.6668262647854, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6e", "roundness": {"type": 3}, "seed": 2057187335, "version": 2317, "versionNonce": 2108528903, "isDeleted": false, "boundElements": [{"type": "text", "id": "dDYNG9DhHrEF6oAswmYYs"}, {"id": "mO1bQlZ03vCyc8PDhsyog", "type": "arrow"}, {"id": "H6PKtQFVvfJv2Nc_5MIa0", "type": "arrow"}], "updated": 1742299517074, "link": null, "locked": false}, {"id": "dDYNG9DhHrEF6oAswmYYs", "type": "text", "x": 11797.287618926635, "y": 1925.031841153269, "width": 185.53985595703125, "height": 50.01720738062966, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6f", "roundness": null, "seed": 864589607, "version": 2295, "versionNonce": 1388503079, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "Create audio clip in\nDB", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "GGMg3bpfSLAFRFyJFOpH3", "originalText": "Create audio clip in DB", "autoResize": true, "lineHeight": 1.25}, {"id": "IX_APthrrzFj6ewzq0V4y", "type": "text", "x": 12593.023486386972, "y": 1853.4092365546508, "width": 163.6598663330078, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6k", "roundness": null, "seed": 83172745, "version": 946, "versionNonce": 1432896103, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "Inngest function", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Inngest function", "autoResize": true, "lineHeight": 1.25}, {"id": "ZzTlGfJQ6d2I97hRv-Z5s", "type": "text", "x": 12590.59890739146, "y": 1639.8561391762555, "width": 8.539993286132812, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6o", "roundness": null, "seed": 1530447687, "version": 508, "versionNonce": 606166407, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "1", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "1", "autoResize": true, "lineHeight": 1.25}, {"id": "tFhZhLRLSLzxglJ5f87Cy", "type": "text", "x": 12635.975817078704, "y": 1639.8561391762555, "width": 14, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6p", "roundness": null, "seed": 1687115817, "version": 510, "versionNonce": 1867125927, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "2", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "2", "autoResize": true, "lineHeight": 1.25}, {"id": "cgDbgzydKgoz-P-c9OAvs", "type": "text", "x": 12683.16780315344, "y": 1639.8561391762555, "width": 12.159988403320312, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6q", "roundness": null, "seed": 1952722791, "version": 508, "versionNonce": 1049552839, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "3", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "3", "autoResize": true, "lineHeight": 1.25}, {"id": "aVS-JNN3pB6B4SATlb1Kf", "type": "text", "x": 12733.989942003152, "y": 1639.8561391762555, "width": 11.699996948242188, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6r", "roundness": null, "seed": 1949438473, "version": 508, "versionNonce": 1879856871, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "4", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "4", "autoResize": true, "lineHeight": 1.25}, {"id": "1bObqbqq_s1efN902LaWs", "type": "text", "x": 12785.335045802556, "y": 1638.8367692787588, "width": 12.3599853515625, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6s", "roundness": null, "seed": 2027698823, "version": 609, "versionNonce": 1783554567, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "5", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "5", "autoResize": true, "lineHeight": 1.25}, {"id": "cB0fOnHDr_HcHSWehwUuW", "type": "text", "x": 12835.25834658565, "y": 1638.6867463704803, "width": 12.79998779296875, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6t", "roundness": null, "seed": 1416761033, "version": 670, "versionNonce": 240322855, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "6", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "6", "autoResize": true, "lineHeight": 1.25}, {"id": "BkmnTuItnA5FpOJo8s_ir", "type": "text", "x": 12882.450332660383, "y": 1638.590800726374, "width": 11.159988403320312, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6u", "roundness": null, "seed": 1656180137, "version": 671, "versionNonce": 1770343495, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "7", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "7", "autoResize": true, "lineHeight": 1.25}, {"id": "Icqw6N-3jEU8PttcMDaDF", "type": "text", "x": 12933.368417154203, "y": 1638.6867463704803, "width": 12.719985961914062, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6v", "roundness": null, "seed": 934391945, "version": 671, "versionNonce": 667036519, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "8", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "8", "autoResize": true, "lineHeight": 1.25}, {"id": "Lr6CnAlrxdZJ-RpKAoxRu", "type": "text", "x": 12984.6175753095, "y": 1637.667376472983, "width": 12.579986572265625, "height": 25, "angle": 0, "strokeColor": "#1971c2", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6w", "roundness": null, "seed": 152876905, "version": 771, "versionNonce": 1163273863, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "9", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "9", "autoResize": true, "lineHeight": 1.25}, {"id": "ZEv7cTTgB4tmUqBPpDBUV", "type": "rectangle", "x": 11762.036685994355, "y": 1885.8024418506002, "width": 265.20283073399565, "height": 251.21886018679663, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b6z", "roundness": {"type": 3}, "seed": 1871549513, "version": 3081, "versionNonce": 1210017511, "isDeleted": false, "boundElements": [{"id": "TGN2_6MJG4zgZMs8xp1kK", "type": "arrow"}], "updated": 1742299757249, "link": null, "locked": false}, {"id": "0Bzw6t_aJ0PAcq0BXSilJ", "type": "text", "x": 11762.634853499383, "y": 1845.1832659031284, "width": 227.31982421875, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b70", "roundness": null, "seed": 1799758633, "version": 1087, "versionNonce": 95010791, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "Generate Server Action", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Generate Server Action", "autoResize": true, "lineHeight": 1.25}, {"id": "jbG0l649AHdomMCNEMgTo", "type": "rectangle", "x": 11787.527411290906, "y": 2030.0307345864571, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b71", "roundness": {"type": 3}, "seed": 2093599753, "version": 2417, "versionNonce": 1216738609, "isDeleted": false, "boundElements": [{"type": "text", "id": "m1YJGppGfVBIeL0tf0lr2"}, {"id": "QCNwp41vDWJHnGSr3Cu__", "type": "arrow"}, {"id": "H6PKtQFVvfJv2Nc_5MIa0", "type": "arrow"}], "updated": 1742578930107, "link": null, "locked": false}, {"id": "m1YJGppGfVBIeL0tf0lr2", "type": "text", "x": 11793.698973967565, "y": 2044.395749474941, "width": 192.4398651123047, "height": 50.01720738062966, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b72", "roundness": null, "seed": 1000694505, "version": 2434, "versionNonce": 592586513, "isDeleted": false, "boundElements": [], "updated": 1742578930107, "link": null, "locked": false, "text": "Insert generate job\ninto queue", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "jbG0l649AHdomMCNEMgTo", "originalText": "Insert generate job into queue", "autoResize": true, "lineHeight": 1.25}, {"id": "mO1bQlZ03vCyc8PDhsyog", "type": "arrow", "x": 11997.449042137961, "y": 1949.940444843584, "width": 217.9862519081471, "height": 307.17929148028793, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b73", "roundness": null, "seed": 1064678793, "version": 1050, "versionNonce": 1146280895, "isDeleted": false, "boundElements": [], "updated": 1742575205712, "link": null, "locked": false, "points": [[0, 0], [108.99312595407355, 0], [108.99312595407355, -307.17929148028793], [217.9862519081471, -307.17929148028793]], "lastCommittedPoint": null, "startBinding": {"elementId": "GGMg3bpfSLAFRFyJFOpH3", "fixedPoint": [1.02441609036293, 0.4987301141778492], "focus": 0, "gap": 0}, "endBinding": {"elementId": "-QvTgKsTC18qbRz0qrDX1", "fixedPoint": [-0.024416090362931745, 0.4987301141778492], "focus": 0, "gap": 0}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": true, "fixedSegments": null, "startIsSpecial": null, "endIsSpecial": null}, {"id": "B5Tn_QckR6Cpw-V_NzMIp", "type": "text", "x": 12036.033337135239, "y": 1958.1599798542056, "width": 111.59991455078125, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b76", "roundness": null, "seed": 1064824327, "version": 491, "versionNonce": 1438148263, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "Returns ID", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Returns ID", "autoResize": true, "lineHeight": 1.25}, {"id": "FKGvhL2VZIJ_GBnhcXHaF", "type": "rectangle", "x": 12020.485550634707, "y": 2642.295142422081, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b78", "roundness": {"type": 3}, "seed": 1603242567, "version": 2379, "versionNonce": 660797895, "isDeleted": false, "boundElements": [{"type": "text", "id": "QRi-3fwPJQtzL-WwCMHEp"}, {"id": "8qW5GRViwEIdKGQBvEdVX", "type": "arrow"}], "updated": 1742299517074, "link": null, "locked": false}, {"id": "QRi-3fwPJQtzL-WwCMHEp", "type": "text", "x": 12087.157075164392, "y": 2669.164459155722, "width": 71.43994140625, "height": 25.00860369031483, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b79", "roundness": null, "seed": 500476263, "version": 2379, "versionNonce": 143534311, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "Playbar", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "FKGvhL2VZIJ_GBnhcXHaF", "originalText": "Playbar", "autoResize": true, "lineHeight": 1.25}, {"id": "UgF8-M8iMQkuWUpD5zGNZ", "type": "rectangle", "x": 11770.657043772775, "y": 2543.7360861978746, "width": 453.5606204001893, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7A", "roundness": {"type": 3}, "seed": 361499303, "version": 2607, "versionNonce": 1551043817, "isDeleted": false, "boundElements": [{"type": "text", "id": "xnIJwP6SdxAIzu-ofC1iV"}, {"id": "_T1zVb50I_ahLwX7CTNEz", "type": "arrow"}], "updated": 1742299541451, "link": null, "locked": false}, {"id": "xnIJwP6SdxAIzu-ofC1iV", "type": "text", "x": 11895.02741897531, "y": 2570.6054029315155, "width": 204.8198699951172, "height": 25.00860369031483, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7B", "roundness": null, "seed": 1965170119, "version": 2629, "versionNonce": 650141255, "isDeleted": false, "boundElements": [], "updated": 1742299517074, "link": null, "locked": false, "text": "Text to speech page", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "UgF8-M8iMQkuWUpD5zGNZ", "originalText": "Text to speech page", "autoResize": true, "lineHeight": 1.25}, {"id": "xJJ1Mag-cnDsllwGflKsl", "type": "ellipse", "x": 12253.684326480019, "y": 2561.8364198737704, "width": 48.51173277471935, "height": 44.51959965969445, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7C", "roundness": {"type": 2}, "seed": 1956212039, "version": 393, "versionNonce": 2020915335, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false}, {"id": "1FirOezBQNnjmRXTv4men", "type": "line", "x": 12302.509485244927, "y": 2580.046295583895, "width": 2.139379814901986, "height": 9.975158677035324, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7D", "roundness": {"type": 2}, "seed": 283889863, "version": 537, "versionNonce": 913969063, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false, "points": [[0, 0], [2.139379814901986, -9.975158677035324]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "DKox0jTmafUfzqf_bEoqp", "type": "line", "x": 12301.372700894035, "y": 2580.994593104321, "width": 10.573079179603155, "height": 5.835960564950938, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7E", "roundness": {"type": 2}, "seed": 1212873737, "version": 681, "versionNonce": 872907463, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false, "points": [[0, 0], [-10.573079179603155, -5.835960564950938]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "tfUIzw7L43vkIBwIf3N5X", "type": "line", "x": 12255.226334696623, "y": 2589.5642946439816, "width": 5.506882443540235, "height": 9.93762691754273, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7F", "roundness": {"type": 2}, "seed": 637217449, "version": 955, "versionNonce": 28938727, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false, "points": [[0, 0], [-5.506882443540235, 9.93762691754273]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "QNK2hU3G6pHw-_6NesGS3", "type": "line", "x": 12256.898947337811, "y": 2589.7982484042045, "width": 8.121361704606898, "height": 5.426547884839238, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7G", "roundness": {"type": 2}, "seed": 376868009, "version": 776, "versionNonce": 1755102471, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false, "points": [[0, 0], [8.121361704606898, 5.426547884839238]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "tQMmY_Ti8rbNZtRFSIt02", "type": "text", "x": 12321.48727284577, "y": 2558.9164329224204, "width": 327.2998046875, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7I", "roundness": null, "seed": 1106672041, "version": 462, "versionNonce": 1546486823, "isDeleted": false, "boundElements": [{"id": "8qW5GRViwEIdKGQBvEdVX", "type": "arrow"}], "updated": 1742299517075, "link": null, "locked": false, "text": "Starts polling status with ID\nWHEN status is DONE, play audio", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Starts polling status with ID\nWHEN status is DONE, play audio", "autoResize": true, "lineHeight": 1.25}, {"id": "fSg2Cy1pWylMQva4ji8xx", "type": "rectangle", "x": 12296.983932003484, "y": 1912.9004293401645, "width": 202.01236866973403, "height": 77.68182244156678, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7J", "roundness": {"type": 3}, "seed": 1209717351, "version": 2558, "versionNonce": 334959561, "isDeleted": false, "boundElements": [{"type": "text", "id": "cpTKBsho3Qmt9R_NnoS2Z"}, {"id": "siHgAfS5HmzKTf8PqKdCF", "type": "arrow"}, {"id": "8ggeEqIRwp_XDtBvmiq33", "type": "arrow"}], "updated": 1742299860692, "link": null, "locked": false}, {"id": "cpTKBsho3Qmt9R_NnoS2Z", "type": "text", "x": 12322.759670476535, "y": 1927.0710920353445, "width": 150.4608917236328, "height": 49.34049705120682, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7K", "roundness": null, "seed": 1107422599, "version": 2593, "versionNonce": 1549697415, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false, "text": "Check audio job\nstatus in DB", "fontSize": 19.736198820482727, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "fSg2Cy1pWylMQva4ji8xx", "originalText": "Check audio job status in DB", "autoResize": true, "lineHeight": 1.25}, {"id": "aRzWDFFAD7qo4sMWGMFao", "type": "rectangle", "x": 12268.11649399879, "y": 1883.693944448859, "width": 262.56578539337204, "height": 285.8955518354685, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7L", "roundness": {"type": 3}, "seed": 580428967, "version": 3623, "versionNonce": 19958377, "isDeleted": false, "boundElements": [{"id": "_T1zVb50I_ahLwX7CTNEz", "type": "arrow"}, {"id": "8ggeEqIRwp_XDtBvmiq33", "type": "arrow"}], "updated": 1742299848363, "link": null, "locked": false}, {"id": "DnKIZmbfSMkwdQuj8SRrt", "type": "text", "x": 12272.565243423316, "y": 1848.078858585493, "width": 197.1163819355613, "height": 24.66176123935054, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7M", "roundness": null, "seed": 697006023, "version": 1364, "versionNonce": 2049702631, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false, "text": "Polling Server Action", "fontSize": 19.729408991480433, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Polling Server Action", "autoResize": true, "lineHeight": 1.25}, {"id": "QCNwp41vDWJHnGSr3Cu__", "type": "arrow", "x": 11997.310401756527, "y": 2069.3043531652556, "width": 563.1200617759841, "height": 419.9701198219352, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7P", "roundness": null, "seed": 484538313, "version": 1498, "versionNonce": 1360876721, "isDeleted": false, "boundElements": [], "updated": 1742578930165, "link": null, "locked": false, "points": [[0, 0], [201.5967584987484, 0], [201.5967584987484, -309.10004787265507], [473.9974507443221, -309.10004787265507], [473.9974507443221, -419.9701198219352], [563.1200617759841, -419.9701198219352]], "lastCommittedPoint": null, "startBinding": {"elementId": "jbG0l649AHdomMCNEMgTo", "fixedPoint": [1.02441609036293, 0.4987301141778492], "focus": 0, "gap": 0}, "endBinding": {"elementId": "gzMs9qfoAbZkkqQ8ZuWr1", "fixedPoint": [-0.011071016512630292, 0.4987301141778492], "focus": 0, "gap": 0}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": true, "fixedSegments": [{"index": 2, "start": [201.5967584987484, 0], "end": [201.5967584987484, -309.10004787265507]}, {"index": 3, "start": [201.5967584987484, -309.10004787265507], "end": [473.9974507443221, -309.10004787265507]}, {"index": 4, "start": [473.9974507443221, -309.10004787265507], "end": [473.9974507443221, -419.9701198219352]}], "startIsSpecial": false, "endIsSpecial": false}, {"id": "TGN2_6MJG4zgZMs8xp1kK", "type": "arrow", "x": 11767.440654310627, "y": 2681.19078542101, "width": 106.50484081975992, "height": 669.8833295071754, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7R", "roundness": null, "seed": 678888103, "version": 1280, "versionNonce": 1837101575, "isDeleted": false, "boundElements": [], "updated": 1742299757249, "link": null, "locked": false, "points": [[0, 0], [-106.50484081975992, 0], [-106.50484081975992, -669.8833295071754], [-10.403968316271857, -669.8833295071754]], "lastCommittedPoint": null, "startBinding": {"elementId": "IY0t59HxLJhJCeTYXFU4p", "fixedPoint": [-0.024416090362931745, 0.4987301141778492], "focus": 0, "gap": 0}, "endBinding": {"elementId": "ZEv7cTTgB4tmUqBPpDBUV", "fixedPoint": [-0.018853494082855818, 0.49958436229634046], "focus": 0, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": true, "fixedSegments": [{"index": 2, "start": [-106.50484081975992, 0], "end": [-106.50484081975992, -669.8833295071754]}], "startIsSpecial": false, "endIsSpecial": false}, {"id": "yPkMOWsq_IhcOt6JDw88M", "type": "text", "x": 11573.316684321557, "y": 2698.763832898713, "width": 140.4398956298828, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7V", "roundness": null, "seed": 1877637385, "version": 385, "versionNonce": 43479111, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false, "text": "ID is returned", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "ID is returned", "autoResize": true, "lineHeight": 1.25}, {"id": "jnsKCQQNmeeIYF3CaA7CB", "type": "text", "x": 11563.564785733433, "y": 1966.0230097258304, "width": 146.119873046875, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7W", "roundness": null, "seed": 694334151, "version": 365, "versionNonce": 586141543, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false, "text": "Press generate", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Press generate", "autoResize": true, "lineHeight": 1.25}, {"id": "8qW5GRViwEIdKGQBvEdVX", "type": "arrow", "x": 12506.810943779226, "y": 2617.1125462600667, "width": 276.5424026788987, "height": 64.45621474081281, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7Y", "roundness": null, "seed": 1464343783, "version": 965, "versionNonce": 1395608199, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false, "points": [[0, 0], [0, 64.45621474081281], [-276.5424026788987, 64.45621474081281]], "lastCommittedPoint": null, "startBinding": {"elementId": "tQMmY_Ti8rbNZtRFSIt02", "fixedPoint": [0.56621992521627, 1.1639222667529294], "focus": 0, "gap": 0}, "endBinding": {"elementId": "FKGvhL2VZIJ_GBnhcXHaF", "fixedPoint": [1.02441609036293, 0.4987301141778492], "focus": 0, "gap": 0}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": true, "fixedSegments": null, "startIsSpecial": null, "endIsSpecial": null}, {"id": "H6PKtQFVvfJv2Nc_5MIa0", "type": "arrow", "x": 11889.04185069958, "y": 1989.5325074430884, "width": 0.69980010112522, "height": 40.13448358431128, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7Z", "roundness": null, "seed": 1562915783, "version": 798, "versionNonce": 1720604369, "isDeleted": false, "boundElements": [], "updated": 1742578930109, "link": null, "locked": false, "points": [[0, 0], [0.69980010112522, 40.13448358431128]], "lastCommittedPoint": null, "startBinding": null, "endBinding": {"elementId": "jbG0l649AHdomMCNEMgTo", "focus": 0.005002230103894085, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false, "fixedSegments": null, "startIsSpecial": null, "endIsSpecial": null}, {"id": "whFJ_VGCcq0oumUoq_6zc", "type": "rectangle", "x": 12300.394203515743, "y": 2060.6570103060358, "width": 202.01236866973403, "height": 77.68182244156678, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7b", "roundness": {"type": 3}, "seed": 1485777191, "version": 2647, "versionNonce": 2061853671, "isDeleted": false, "boundElements": [{"type": "text", "id": "ZDexvaINmA8lFM_ZhR3L7"}, {"id": "siHgAfS5HmzKTf8PqKdCF", "type": "arrow"}], "updated": 1742299517075, "link": null, "locked": false}, {"id": "ZDexvaINmA8lFM_ZhR3L7", "type": "text", "x": 12317.44929989895, "y": 2074.8276730012158, "width": 167.9021759033203, "height": 49.34049705120682, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7c", "roundness": null, "seed": 1463643207, "version": 2713, "versionNonce": 813584135, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false, "text": "Return generated\naudio URL", "fontSize": 19.736198820482727, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "whFJ_VGCcq0oumUoq_6zc", "originalText": "Return generated audio URL", "autoResize": true, "lineHeight": 1.25}, {"id": "fMQYhQFh30F06FNx7v1TN", "type": "text", "x": 12318.045963582237, "y": 2010.8245201272089, "width": 68.17994689941406, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7d", "roundness": null, "seed": 1452973447, "version": 494, "versionNonce": 1383954759, "isDeleted": false, "boundElements": [{"id": "siHgAfS5HmzKTf8PqKdCF", "type": "arrow"}], "updated": 1742299517075, "link": null, "locked": false, "text": "if done", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "if done", "autoResize": true, "lineHeight": 1.25}, {"id": "siHgAfS5HmzKTf8PqKdCF", "type": "arrow", "x": 12398.44775156496, "y": 1992.8974933703394, "width": 0.7048350100758398, "height": 66.19848037933548, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7e", "roundness": null, "seed": 260779561, "version": 985, "versionNonce": 1678447049, "isDeleted": false, "boundElements": [], "updated": 1742299517626, "link": null, "locked": false, "points": [[0, 0], [0.7048350100758398, 66.19848037933548]], "lastCommittedPoint": null, "startBinding": {"elementId": "fMQYhQFh30F06FNx7v1TN", "focus": 0, "gap": 14}, "endBinding": {"elementId": "whFJ_VGCcq0oumUoq_6zc", "focus": -0.017921853744533898, "gap": 1.561036556360932}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false, "fixedSegments": null, "startIsSpecial": null, "endIsSpecial": null}, {"id": "ZJNa9mvnTsPaBu8MS-jTO", "type": "arrow", "x": 12588.210285434183, "y": 1689.7824207884153, "width": 205.1975851581392, "height": 188.69615789578313, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7g", "roundness": null, "seed": 1058517609, "version": 1165, "versionNonce": 2062092967, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false, "points": [[0, 0], [0, 96.63197773324407], [205.1975851581392, 96.63197773324407], [205.1975851581392, 188.69615789578313]], "lastCommittedPoint": null, "startBinding": {"elementId": "gzMs9qfoAbZkkqQ8ZuWr1", "fixedPoint": [0.05043915688563605, 1.012375911860196], "focus": 0, "gap": 0}, "endBinding": {"elementId": "gW3_mgt_qawxb4tkw_wU4", "fixedPoint": [0.7225779094234986, -0.020199448906474383], "focus": 0, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": true, "fixedSegments": null, "startIsSpecial": null, "endIsSpecial": null}, {"id": "ljOI-cLUoUBt3P2HpiyNW", "type": "text", "x": 12612.903256995516, "y": 1746.3052409785737, "width": 311.1397705078125, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7h", "roundness": null, "seed": 1973034409, "version": 381, "versionNonce": 652156359, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false, "text": "Run function per queue message", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Run function per queue message", "autoResize": true, "lineHeight": 1.25}, {"id": "8h-0ArIgbOXUVq0J0Wd6Z", "type": "rectangle", "x": 12623.090911424828, "y": 1912.3290849086547, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7i", "roundness": {"type": 3}, "seed": 2088391367, "version": 1752, "versionNonce": 1136666193, "isDeleted": false, "boundElements": [{"type": "text", "id": "3mDQwqCreL9Vm5RzADjcX"}, {"id": "DoO0J8xhWAgUDS4MCDJr-", "type": "arrow"}, {"id": "ejaNMEABiP8C64sJcb-jF", "type": "arrow"}], "updated": 1742575126296, "link": null, "locked": false}, {"id": "3mDQwqCreL9Vm5RzADjcX", "type": "text", "x": 12681.302444499435, "y": 1939.1984016422957, "width": 88.35992431640625, "height": 25.00860369031483, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7j", "roundness": null, "seed": 1397907943, "version": 1726, "versionNonce": 1449735217, "isDeleted": false, "boundElements": [], "updated": 1742575126296, "link": null, "locked": false, "text": "Generate", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "8h-0ArIgbOXUVq0J0Wd6Z", "originalText": "Generate", "autoResize": true, "lineHeight": 1.25}, {"id": "DoO0J8xhWAgUDS4MCDJr-", "type": "arrow", "x": 12830.19109677539, "y": 1950.8394188068937, "width": 172.15972244240766, "height": 0.06149718927986214, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7k", "roundness": null, "seed": 1228460391, "version": 1096, "versionNonce": 813008401, "isDeleted": false, "boundElements": [], "updated": 1742575126297, "link": null, "locked": false, "points": [[0, 0], [172.15972244240766, 0.06149718927986214]], "lastCommittedPoint": null, "startBinding": {"elementId": "8h-0ArIgbOXUVq0J0Wd6Z", "focus": -0.022854180528622394, "gap": 2.4949903907363478}, "endBinding": {"elementId": "xV-EVo4SFUjlIN6rxvgRE", "focus": -0.04518188770537536, "gap": 1.220774333922236}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false, "fixedSegments": null, "startIsSpecial": null, "endIsSpecial": null}, {"id": "eIst5bUy15pYpIfyiRX-1", "type": "rectangle", "x": 12626.206889006124, "y": 2047.6118331155271, "width": 204.7829904656213, "height": 78.74723715759697, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7l", "roundness": {"type": 3}, "seed": 32558663, "version": 1938, "versionNonce": 1921105545, "isDeleted": false, "boundElements": [{"type": "text", "id": "lGqQFUjWxR9WHgQBCgvhn"}, {"id": "ejaNMEABiP8C64sJcb-jF", "type": "arrow"}, {"id": "MfrdcTFxN9lzZWoXfe4Cy", "type": "arrow"}], "updated": 1742299935889, "link": null, "locked": false}, {"id": "lGqQFUjWxR9WHgQBCgvhn", "type": "text", "x": 12637.228442527508, "y": 2061.9768480040107, "width": 182.73988342285156, "height": 50.01720738062966, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7m", "roundness": null, "seed": 1802333543, "version": 1938, "versionNonce": 1707520935, "isDeleted": false, "boundElements": [], "updated": 1742299517075, "link": null, "locked": false, "text": "Update job status\nin DB", "fontSize": 20.006882952251864, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "eIst5bUy15pYpIfyiRX-1", "originalText": "Update job status in DB", "autoResize": true, "lineHeight": 1.25}, {"id": "ejaNMEABiP8C64sJcb-jF", "type": "arrow", "x": 12729.256723661343, "y": 1991.440065625309, "width": 0.2768119798092812, "height": 55.26569766229568, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7n", "roundness": null, "seed": 1980409703, "version": 951, "versionNonce": 215784433, "isDeleted": false, "boundElements": [], "updated": 1742575126297, "link": null, "locked": false, "points": [[0, 0], [-0.2768119798092812, 55.26569766229568]], "lastCommittedPoint": null, "startBinding": {"elementId": "8h-0ArIgbOXUVq0J0Wd6Z", "focus": -0.038738221225973805, "gap": 1}, "endBinding": {"elementId": "eIst5bUy15pYpIfyiRX-1", "focus": 0.0020228216248330483, "gap": 2.445486918406459}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "ZHqA232ohATEX3_twpzAr", "type": "rectangle", "x": 11541.695756601335, "y": 1053.1812134387771, "width": 277.78916537329224, "height": 101.85682924688672, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7o", "roundness": {"type": 3}, "seed": 24040777, "version": 555, "versionNonce": 1822404007, "isDeleted": false, "boundElements": [{"type": "text", "id": "x3AVWflwrAVIyn8nPr-sH"}, {"id": "xy5nJSqcTVE7g-9qeNFZg", "type": "arrow"}], "updated": 1742299672043, "link": null, "locked": false}, {"id": "x3AVWflwrAVIyn8nPr-sH", "type": "text", "x": 11605.280402764543, "y": 1091.6096280622205, "width": 150.619873046875, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7p", "roundness": null, "seed": 290931753, "version": 552, "versionNonce": 264855751, "isDeleted": false, "boundElements": [], "updated": 1742299672043, "link": null, "locked": false, "text": "Press Generate", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "ZHqA232ohATEX3_twpzAr", "originalText": "Press Generate", "autoResize": true, "lineHeight": 1.25}, {"id": "7-G3cdfNXix0IGrtTE0WE", "type": "rectangle", "x": 11910.029412930247, "y": 1052.4880551872764, "width": 277.78916537329224, "height": 101.85682924688672, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7q", "roundness": {"type": 3}, "seed": 85600583, "version": 680, "versionNonce": 134159111, "isDeleted": false, "boundElements": [{"type": "text", "id": "iqFhvMkNAFSr7VgMzhLeL"}, {"id": "siiTWBRgIL1ca_lxqsOy7", "type": "arrow"}, {"id": "xy5nJSqcTVE7g-9qeNFZg", "type": "arrow"}, {"id": "slE2LsVK_YoVCBKNNHf4W", "type": "arrow"}], "updated": 1742299672043, "link": null, "locked": false}, {"id": "iqFhvMkNAFSr7VgMzhLeL", "type": "text", "x": 11942.174064281444, "y": 1078.4164698107197, "width": 213.49986267089844, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7r", "roundness": null, "seed": 380856423, "version": 727, "versionNonce": 1536434727, "isDeleted": false, "boundElements": [], "updated": 1742299672043, "link": null, "locked": false, "text": "Create job in DB with\nunfinished status", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "7-G3cdfNXix0IGrtTE0WE", "originalText": "Create job in DB with unfinished status", "autoResize": true, "lineHeight": 1.25}, {"id": "EcLQEswlHt1m4mLnQL9vH", "type": "rectangle", "x": 11911.047401282449, "y": 1210.0717397559583, "width": 277.78916537329224, "height": 101.85682924688672, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7s", "roundness": {"type": 3}, "seed": 1072717897, "version": 898, "versionNonce": 208295591, "isDeleted": false, "boundElements": [{"type": "text", "id": "q60IyXoZFzNUnA1jjK5wR"}, {"id": "siiTWBRgIL1ca_lxqsOy7", "type": "arrow"}], "updated": 1742299672043, "link": null, "locked": false}, {"id": "q60IyXoZFzNUnA1jjK5wR", "type": "text", "x": 11937.76205232847, "y": 1236.0001543794017, "width": 224.35986328125, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7t", "roundness": null, "seed": 1030713129, "version": 982, "versionNonce": 307685831, "isDeleted": false, "boundElements": [], "updated": 1742299672043, "link": null, "locked": false, "text": "Push job to generation\nqueue", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "EcLQEswlHt1m4mLnQL9vH", "originalText": "Push job to generation queue", "autoResize": true, "lineHeight": 1.25}, {"id": "aUt18DLeJlvN3tzOc7jTo", "type": "rectangle", "x": 12299.314247194869, "y": 1049.9277427779866, "width": 277.78916537329224, "height": 101.85682924688672, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7w", "roundness": {"type": 3}, "seed": 637032935, "version": 894, "versionNonce": 1233019911, "isDeleted": false, "boundElements": [{"type": "text", "id": "Zr2eUugRBNAJtB0KoCsd3"}, {"id": "GsnH97JMAbd-ERbKM0whI", "type": "arrow"}, {"id": "slE2LsVK_YoVCBKNNHf4W", "type": "arrow"}, {"id": "zaFgLMSgqC9x9lWSHg61D", "type": "arrow"}], "updated": 1742299672043, "link": null, "locked": false}, {"id": "Zr2eUugRBNAJtB0KoCsd3", "type": "text", "x": 12316.228918077315, "y": 1075.85615740143, "width": 243.95982360839844, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7x", "roundness": null, "seed": 700613895, "version": 1006, "versionNonce": 541215527, "isDeleted": false, "boundElements": [], "updated": 1742299672043, "link": null, "locked": false, "text": "Queue message is\nprocessed (run AI model)", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "aUt18DLeJlvN3tzOc7jTo", "originalText": "Queue message is processed (run AI model)", "autoResize": true, "lineHeight": 1.25}, {"id": "mUxOYHoiKA1PXbffTba-a", "type": "rectangle", "x": 12693.974106958382, "y": 1050.2818282526985, "width": 277.78916537329224, "height": 101.85682924688672, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7y", "roundness": {"type": 3}, "seed": 89803815, "version": 1341, "versionNonce": 675020711, "isDeleted": false, "boundElements": [{"type": "text", "id": "MdlRWtM1ljNEKmaQKJKvt"}, {"id": "zaFgLMSgqC9x9lWSHg61D", "type": "arrow"}], "updated": 1742299672043, "link": null, "locked": false}, {"id": "MdlRWtM1ljNEKmaQKJKvt", "type": "text", "x": 12723.738768685555, "y": 1076.2102428761418, "width": 218.2598419189453, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b7z", "roundness": null, "seed": 615264071, "version": 1457, "versionNonce": 2014016199, "isDeleted": false, "boundElements": [], "updated": 1742299672043, "link": null, "locked": false, "text": "Frontend can now play\naudio", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "mUxOYHoiKA1PXbffTba-a", "originalText": "Frontend can now play audio", "autoResize": true, "lineHeight": 1.25}, {"id": "GYStT1x4IlF4N4oVwOtyZ", "type": "rectangle", "x": 12303.22028210574, "y": 1206.9909400505392, "width": 277.78916537329224, "height": 101.85682924688672, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b80", "roundness": {"type": 3}, "seed": 1228884585, "version": 1137, "versionNonce": 275033351, "isDeleted": false, "boundElements": [{"type": "text", "id": "6Xdz84ENJDUB8GPaDYQxe"}, {"id": "GsnH97JMAbd-ERbKM0whI", "type": "arrow"}], "updated": 1742299672043, "link": null, "locked": false}, {"id": "6Xdz84ENJDUB8GPaDYQxe", "type": "text", "x": 12317.874966110745, "y": 1220.4193546739825, "width": 248.47979736328125, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b81", "roundness": null, "seed": 765203785, "version": 1311, "versionNonce": 628585511, "isDeleted": false, "boundElements": [], "updated": 1742299672043, "link": null, "locked": false, "text": "Update job in db to\nfinished status, and save\naudio id", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "GYStT1x4IlF4N4oVwOtyZ", "originalText": "Update job in db to finished status, and save audio id", "autoResize": true, "lineHeight": 1.25}, {"id": "siiTWBRgIL1ca_lxqsOy7", "type": "arrow", "x": 12046.929193087191, "y": 1152.988540866254, "width": 0, "height": 57.18181881491864, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b82", "roundness": null, "seed": 377883335, "version": 71, "versionNonce": 272461001, "isDeleted": false, "boundElements": [], "updated": 1742299673010, "link": null, "locked": false, "points": [[0, 0], [0, 57.18181881491864]], "lastCommittedPoint": null, "startBinding": {"elementId": "7-G3cdfNXix0IGrtTE0WE", "focus": 0.014361989439157148, "gap": 1.3563435679091072}, "endBinding": {"elementId": "EcLQEswlHt1m4mLnQL9vH", "focus": -0.021691205111289816, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "GsnH97JMAbd-ERbKM0whI", "type": "arrow", "x": 12447.277139765321, "y": 1152.1325675646112, "width": 0.09460023445171828, "height": 53.9180746627876, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b83", "roundness": null, "seed": 1399324999, "version": 111, "versionNonce": 1656589961, "isDeleted": false, "boundElements": [], "updated": 1742299673011, "link": null, "locked": false, "points": [[0, 0], [0.09460023445171828, 53.9180746627876]], "lastCommittedPoint": null, "startBinding": {"elementId": "aUt18DLeJlvN3tzOc7jTo", "focus": -0.06459987145498514, "gap": 1}, "endBinding": {"elementId": "GYStT1x4IlF4N4oVwOtyZ", "focus": 0.03847840185514016, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "xy5nJSqcTVE7g-9qeNFZg", "type": "arrow", "x": 11815.760401063179, "y": 1107.252607049612, "width": 93.03607057422596, "height": 0, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b86", "roundness": null, "seed": 500148775, "version": 85, "versionNonce": 1836073033, "isDeleted": false, "boundElements": [], "updated": 1742299673011, "link": null, "locked": false, "points": [[0, 0], [93.03607057422596, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "ZHqA232ohATEX3_twpzAr", "focus": 0.061713662414786256, "gap": 3.724520911449872}, "endBinding": {"elementId": "7-G3cdfNXix0IGrtTE0WE", "focus": -0.07532410477050272, "gap": 1.232941292842952}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "slE2LsVK_YoVCBKNNHf4W", "type": "arrow", "x": 12189.090909044151, "y": 1102.796966541422, "width": 109.28584196175325, "height": 1.3587596805045905, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b87", "roundness": null, "seed": 750308713, "version": 92, "versionNonce": 284262921, "isDeleted": false, "boundElements": [], "updated": 1742299673011, "link": null, "locked": false, "points": [[0, 0], [109.28584196175325, 1.3587596805045905]], "lastCommittedPoint": null, "startBinding": {"elementId": "7-G3cdfNXix0IGrtTE0WE", "focus": -0.0448617651592851, "gap": 1.272330740610414}, "endBinding": {"elementId": "aUt18DLeJlvN3tzOc7jTo", "focus": -0.09568100683642561, "gap": 1}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "zaFgLMSgqC9x9lWSHg61D", "type": "arrow", "x": 12578.040908757126, "y": 1103.0005717057227, "width": 112.24441269955423, "height": 0.7618073565124632, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b88", "roundness": null, "seed": 405597031, "version": 152, "versionNonce": 920474569, "isDeleted": false, "boundElements": [], "updated": 1742299673011, "link": null, "locked": false, "points": [[0, 0], [112.24441269955423, -0.7618073565124632]], "lastCommittedPoint": null, "startBinding": {"elementId": "aUt18DLeJlvN3tzOc7jTo", "focus": 0.059637455106909425, "gap": 1}, "endBinding": {"elementId": "mUxOYHoiKA1PXbffTba-a", "focus": -0.001172182670516156, "gap": 3.6887855017012043}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": false}, {"id": "0WfmGUXKSpD_HpIDyKa3K", "type": "line", "x": 11543.112632727538, "y": 1474.3430732009504, "width": 1671.2195388379023, "height": 6.660445277223744, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b89", "roundness": {"type": 2}, "seed": 19187655, "version": 287, "versionNonce": 1753968201, "isDeleted": false, "boundElements": [], "updated": 1742299527126, "link": null, "locked": false, "points": [[0, 0], [1671.2195388379023, -6.660445277223744]], "lastCommittedPoint": null, "startBinding": null, "endBinding": null, "startArrowhead": null, "endArrowhead": null}, {"id": "qmmlmDebf6SQt5wNYW8lg", "type": "text", "x": 11542.424383901995, "y": 1440.6497214886424, "width": 193.95985412597656, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8A", "roundness": null, "seed": 1421221159, "version": 69, "versionNonce": 1073028071, "isDeleted": false, "boundElements": [], "updated": 1742299511956, "link": null, "locked": false, "text": "In-depth breakdown", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "In-depth breakdown", "autoResize": true, "lineHeight": 1.25}, {"id": "_T1zVb50I_ahLwX7CTNEz", "type": "arrow", "x": 11997.33735397287, "y": 2538.7360861978746, "width": 401.96203272260755, "height": 364.1465899135469, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8B", "roundness": null, "seed": 1187150183, "version": 42, "versionNonce": 1513326537, "isDeleted": false, "boundElements": [], "updated": 1742299541452, "link": null, "locked": false, "points": [[0, 0], [0, -182.07329495677322], [401.96203272260755, -182.07329495677322], [401.96203272260755, -364.1465899135469]], "lastCommittedPoint": null, "startBinding": {"elementId": "UgF8-M8iMQkuWUpD5zGNZ", "fixedPoint": [0.49977952230528216, -0.0634942911075533], "focus": 0, "gap": 0}, "endBinding": {"elementId": "aRzWDFFAD7qo4sMWGMFao", "fixedPoint": [0.49961914306599464, 1.0174889044894189], "focus": 0, "gap": 0}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": true, "fixedSegments": null, "startIsSpecial": null, "endIsSpecial": null}, {"id": "8ggeEqIRwp_XDtBvmiq33", "type": "arrow", "x": 12291.983932003484, "y": 1951.641340560948, "width": 55.62093777963673, "height": 266.85713703972533, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8E", "roundness": null, "seed": 1602172551, "version": 369, "versionNonce": 1434902559, "isDeleted": false, "boundElements": [], "updated": 1742575205763, "link": null, "locked": false, "points": [[0, 0], [-55.62093777963673, 0], [-55.62093777963673, -227.38745175570966], [-39.886555885288544, -227.38745175570966], [-39.886555885288544, -266.85713703972533]], "lastCommittedPoint": null, "startBinding": {"elementId": "fSg2Cy1pWylMQva4ji8xx", "fixedPoint": [-0.024750959720562456, 0.4987126975545014], "focus": 0, "gap": 0}, "endBinding": {"elementId": "-QvTgKsTC18qbRz0qrDX1", "focus": 0.7023432681267544, "gap": 1.8765208507322768, "fixedPoint": [0.15461285139012887, 1.0323748701687918]}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": true, "fixedSegments": [{"index": 2, "start": [-55.62093777963673, 0], "end": [-55.62093777963673, -227.38745175570966]}], "startIsSpecial": false, "endIsSpecial": false}, {"id": "Xxer6rub7s8jFcO06AEfu", "type": "text", "x": 12160.914588043011, "y": 2373.3074456415034, "width": 125.25993347167969, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8F", "roundness": null, "seed": 329853737, "version": 47, "versionNonce": 104268391, "isDeleted": false, "boundElements": [], "updated": 1742299899886, "link": null, "locked": false, "text": "Every 500ms", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Every 500ms", "autoResize": true, "lineHeight": 1.25}, {"id": "MfrdcTFxN9lzZWoXfe4Cy", "type": "arrow", "x": 12621.206889006124, "y": 2086.8854516943256, "width": 234.2682684505744, "height": 401.90532088682244, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8G", "roundness": null, "seed": 22877705, "version": 164, "versionNonce": 320046143, "isDeleted": false, "boundElements": [], "updated": 1742575205763, "link": null, "locked": false, "points": [[0, 0], [-66.10162919175855, 0], [-66.10162919175855, -299.4524151666865], [-234.2682684505744, -299.4524151666865], [-234.2682684505744, -401.90532088682244]], "lastCommittedPoint": null, "startBinding": {"elementId": "eIst5bUy15pYpIfyiRX-1", "fixedPoint": [-0.024416090362931745, 0.4987301141778492], "focus": 0, "gap": 0}, "endBinding": {"elementId": "-QvTgKsTC18qbRz0qrDX1", "fixedPoint": [0.8130720531566468, 1.03486292299899], "focus": 0, "gap": 0}, "startArrowhead": null, "endArrowhead": "arrow", "elbowed": true, "fixedSegments": [{"index": 2, "start": [-66.10162919175855, 0], "end": [-66.10162919175855, -299.4524151666865]}, {"index": 3, "start": [-66.10162919175855, -299.4524151666865], "end": [-234.2682684505744, -299.4524151666865]}], "startIsSpecial": false, "endIsSpecial": false}, {"id": "vFH0KjfM2HBuXEDRI5Wt_", "type": "text", "x": 13967.019033055467, "y": 919.7502975269464, "width": 374.8677978515625, "height": 45, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 4, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8H", "roundness": null, "seed": 141671729, "version": 3085, "versionNonce": 1671842929, "isDeleted": false, "boundElements": [], "updated": 1742589907782, "link": null, "locked": false, "text": "Tasks you should try", "fontSize": 36, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Tasks you should try", "autoResize": true, "lineHeight": 1.25}, {"id": "j_X-356Y6PiyCfay2xO6C", "type": "rectangle", "x": 13980.979685586331, "y": 1991.6884849404896, "width": 435.*************, "height": 124.*************, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8I", "roundness": {"type": 3}, "seed": 352253457, "version": 940, "versionNonce": 1966574737, "isDeleted": false, "boundElements": [{"type": "text", "id": "6CH42jcULuVy2iMINRZwc"}], "updated": *************, "link": null, "locked": false}, {"id": "6CH42jcULuVy2iMINRZwc", "type": "text", "x": 14012.569343593585, "y": 2028.803215070914, "width": 372.**************, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8J", "roundness": null, "seed": **********, "version": 941, "versionNonce": *********, "isDeleted": false, "boundElements": [], "updated": *************, "link": null, "locked": false, "text": "Deploy on Vercel with a free Neon\ndatabase, and a free Inngest account", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "j_X-356Y6PiyCfay2xO6C", "originalText": "Deploy on Vercel with a free Neon database, and a free Inngest account", "autoResize": true, "lineHeight": 1.25}, {"id": "87r-yE4zNyMtIOlRhwQJV", "type": "rectangle", "x": 14532.************, "y": 1131.***********, "width": 435.*************, "height": 124.*************, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8K", "roundness": {"type": 3}, "seed": **********, "version": 1046, "versionNonce": **********, "isDeleted": false, "boundElements": [{"type": "text", "id": "KNqdQAW4xSvY0ao8ndH0I"}], "updated": *************, "link": null, "locked": false}, {"id": "KNqdQAW4xSvY0ao8ndH0I", "type": "text", "x": 14556.***********, "y": 1156.*************, "width": 387.**************, "height": 75, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8L", "roundness": null, "seed": 1147889201, "version": 1048, "versionNonce": 1082219551, "isDeleted": false, "boundElements": [], "updated": 1742590086173, "link": null, "locked": false, "text": "Implement Make-an-audio 2:\nhttps://github.com/bytedance/Make-An-\nAudio-2", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "87r-yE4zNyMtIOlRhwQJV", "originalText": "Implement Make-an-audio 2: https://github.com/bytedance/Make-An-Audio-2", "autoResize": true, "lineHeight": 1.25}, {"id": "hQSOkNdrekusKvZIlt9XR", "type": "rectangle", "x": 13979.445844781316, "y": 1309.2927941095832, "width": 435.*************, "height": 124.*************, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8M", "roundness": {"type": 3}, "seed": 1518342591, "version": 1070, "versionNonce": 447620625, "isDeleted": false, "boundElements": [{"type": "text", "id": "-o3VMKSKk5YaVp0YozVHo"}], "updated": 1742590266039, "link": null, "locked": false}, {"id": "-o3VMKSKk5YaVp0YozVHo", "type": "text", "x": 13984.80550706103, "y": 1346.4075242400074, "width": 424.8597106933594, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8N", "roundness": null, "seed": 825590239, "version": 1090, "versionNonce": 1577548913, "isDeleted": false, "boundElements": [], "updated": 1742590248934, "link": null, "locked": false, "text": "Load voices dynamically from API endpoints\ninstead of hardcoding them", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "hQSOkNdrekusKvZIlt9XR", "originalText": "Load voices dynamically from API endpoints instead of hardcoding them", "autoResize": true, "lineHeight": 1.25}, {"id": "DSeeqOM_wquvKS78For9J", "type": "rectangle", "x": 13978.856679411107, "y": 1474.009126666502, "width": 435.*************, "height": 124.*************, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8O", "roundness": {"type": 3}, "seed": 1275588095, "version": 1151, "versionNonce": 949736511, "isDeleted": false, "boundElements": [{"type": "text", "id": "paNp4toRqmFct-HzO_Mbc"}], "updated": 1742590278650, "link": null, "locked": false}, {"id": "paNp4toRqmFct-HzO_Mbc", "type": "text", "x": 14044.556322769922, "y": 1523.6238567969262, "width": 304.17974853515625, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8P", "roundness": null, "seed": 368563743, "version": 1154, "versionNonce": 189715615, "isDeleted": false, "boundElements": [], "updated": 1742590086173, "link": null, "locked": false, "text": "Recharge users credits monthly", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "DSeeqOM_wquvKS78For9J", "originalText": "Recharge users credits monthly", "autoResize": true, "lineHeight": 1.25}, {"id": "WmZG2bfTg3n8Y4Wh0BNFf", "type": "rectangle", "x": 13979.9460337743, "y": 1649.2410090772885, "width": 435.*************, "height": 124.*************, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8Q", "roundness": {"type": 3}, "seed": 1057339601, "version": 1182, "versionNonce": 1175427793, "isDeleted": false, "boundElements": [{"type": "text", "id": "CLmnDGE7iI8VwgoUkSdbO"}], "updated": 1742590343118, "link": null, "locked": false}, {"id": "CLmnDGE7iI8VwgoUkSdbO", "type": "text", "x": 13993.345689340147, "y": 1686.3557392077128, "width": 408.77972412109375, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8R", "roundness": null, "seed": 676886193, "version": 1230, "versionNonce": 936777905, "isDeleted": false, "boundElements": [], "updated": 1742590343118, "link": null, "locked": false, "text": "Deduct credits based on the service used\ninstead of 50 regardless of the service", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "WmZG2bfTg3n8Y4Wh0BNFf", "originalText": "Deduct credits based on the service used instead of 50 regardless of the service", "autoResize": true, "lineHeight": 1.25}, {"id": "hAZ43E3QWnph5IIYqRP1U", "type": "rectangle", "x": 13979.421503071339, "y": 1819.3154451866292, "width": 435.*************, "height": 124.*************, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8S", "roundness": {"type": 3}, "seed": 945322129, "version": 1277, "versionNonce": 1094921055, "isDeleted": false, "boundElements": [{"type": "text", "id": "sdgLFzVd0zs2kWI2NOk-3"}], "updated": 1742590393256, "link": null, "locked": false}, {"id": "sdgLFzVd0zs2kWI2NOk-3", "type": "text", "x": 14018.951148261209, "y": 1856.*************, "width": 356.5197448730469, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8T", "roundness": null, "seed": 817708657, "version": 1290, "versionNonce": 1697095249, "isDeleted": false, "boundElements": [], "updated": 1742590390947, "link": null, "locked": false, "text": "Prevent the user from accessing the\nservice, if no credits", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "hAZ43E3QWnph5IIYqRP1U", "originalText": "Prevent the user from accessing the service, if no credits", "autoResize": true, "lineHeight": 1.25}, {"id": "xj1jO9XkXu06ps1ONlfTX", "type": "rectangle", "x": 14534.699795739518, "y": 1313.*************, "width": 435.*************, "height": 124.*************, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8U", "roundness": {"type": 3}, "seed": 323890943, "version": 1417, "versionNonce": 1239022257, "isDeleted": false, "boundElements": [{"type": "text", "id": "FepV3SADqSsVS2g87Vn4a"}], "updated": 1742590561865, "link": null, "locked": false}, {"id": "FepV3SADqSsVS2g87Vn4a", "type": "text", "x": 14541.389459850287, "y": 1350.5823919237478, "width": 422.19970703125, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8V", "roundness": null, "seed": 2093872927, "version": 1480, "versionNonce": 1215520927, "isDeleted": false, "boundElements": [], "updated": 1742590559973, "link": null, "locked": false, "text": "Load models dynamically based on the voice\n(styletts2)", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "xj1jO9XkXu06ps1ONlfTX", "originalText": "Load models dynamically based on the voice (styletts2)", "autoResize": true, "lineHeight": 1.25}, {"id": "PM-60n2kFoxLvjuOfy8NT", "type": "rectangle", "x": 13985.31553889184, "y": 1131.3611840184792, "width": 435.*************, "height": 124.*************, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8W", "roundness": {"type": 3}, "seed": 125208017, "version": 1594, "versionNonce": 1186338193, "isDeleted": false, "boundElements": [{"type": "text", "id": "c4WsKXlipszCJjrJCRJPr"}], "updated": 1742590268380, "link": null, "locked": false}, {"id": "c4WsKXlipszCJjrJCRJPr", "type": "text", "x": 13994.82517980925, "y": 1168.4759141489033, "width": 416.55975341796875, "height": 50, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8X", "roundness": null, "seed": 589800369, "version": 1712, "versionNonce": 1820535743, "isDeleted": false, "boundElements": [], "updated": 1742590267069, "link": null, "locked": false, "text": "Download button next to generate button\ndoesn't work", "fontSize": 20, "fontFamily": 5, "textAlign": "center", "verticalAlign": "middle", "containerId": "PM-60n2kFoxLvjuOfy8NT", "originalText": "Download button next to generate button doesn't work", "autoResize": true, "lineHeight": 1.25}, {"id": "WYH1t1p0lkaERSQiGXUp_", "type": "text", "x": 14712.180440739809, "y": 1081.0236409423178, "width": 80.83992004394531, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8a", "roundness": null, "seed": 298274577, "version": 89, "versionNonce": 1643444671, "isDeleted": false, "boundElements": null, "updated": 1742590086173, "link": null, "locked": false, "text": "Backend", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Backend", "autoResize": true, "lineHeight": 1.25}, {"id": "brGPy9j-XFHWz2Zdfspu7", "type": "text", "x": 14150.563267606718, "y": 1075.9085331434967, "width": 88.39993286132812, "height": 25, "angle": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "groupIds": [], "frameId": null, "index": "b8b", "roundness": null, "seed": 1424417087, "version": 247, "versionNonce": 253300817, "isDeleted": false, "boundElements": [], "updated": 1742590160883, "link": null, "locked": false, "text": "Frontend", "fontSize": 20, "fontFamily": 5, "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Frontend", "autoResize": true, "lineHeight": 1.25}], "appState": {"gridSize": 20, "gridStep": 5, "gridModeEnabled": false, "viewBackgroundColor": "#ffffff"}, "files": {}}