{"cells": [{"cell_type": "markdown", "metadata": {"id": "p_g_pzkJYGbM"}, "source": ["# Dataset Analysis and Curation\n", "\n", "This is from from This guy's notebook [here](https://www.kaggle.com/code/maxbr0wn/fine-tuning-xtts-v2-english?scriptVersionId=173570803) and <PERSON><PERSON>'s TTS notebook [here](https://github.com/coqui-ai/TTS/blob/dev/notebooks/dataset_analysis/AnalyzeDataset.ipynb)\n", "\n", "### The only input you'll be required to give is the path to the dataset folder. Everything else should be automated. Including the curation/removal of poor quality samples.\n", "\n", "### Use this notebook with the provided known good dataset first. Then you can compare your own dataset.\n", "\n", "You will need to combine your train_list.txt and val_list.txt into a single \"metadata.csv\" file. The csv needs to be formatted without a header and in this order --> \n", "audio_file_name|transcription|transcription\n", "## Dont use the IPA translation.\n", "\n", "**Example:**\n", "audio_000001|It contained a bookcase: I soon possessed myself of a volume, taking care that it should be one stored with pictures.|It contained a bookcase: I soon possessed myself of a volume, taking care that it should be one stored with pictures.\n", "\n", "You can continue through each cell with the example dataset until it tells you to stop. Theres no need to do the cells beyond that point.\n", "\n", "When you are ready to use your own dataset youll have the option to remove the poor quality samples and regenerate the csv. At that point you can recreate the train_list and val_list.txt and phonemize."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# The cell below is to grab the reference good quality dataset. It can be skipped without any other modifications."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "from tempfile import NamedTemporaryFile\n", "from urllib.request import urlopen\n", "from urllib.parse import unquote, urlparse\n", "from urllib.error import HTTPError\n", "from zipfile import ZipFile\n", "import tarfile\n", "import shutil\n", "\n", "CHUNK_SIZE = 40960\n", "DATA_SOURCE_MAPPING = 'janeeyre:https%3A%2F%2Fstorage.googleapis.com%2Fkaggle-data-sets%2F4372173%2F7507317%2Fbundle%2Farchive.zip%3FX-Goog-Algorithm%3DGOOG4-RSA-SHA256%26X-Goog-Credential%3Dgcp-kaggle-com%2540kaggle-161607.iam.gserviceaccount.com%252F20240608%252Fauto%252Fstorage%252Fgoog4_request%26X-Goog-Date%3D20240608T194313Z%26X-Goog-Expires%3D259200%26X-Goog-SignedHeaders%3Dhost%26X-Goog-Signature%3D21443d92e1d8791f65ec5a33925c559685283f40417a61348248dc6fd8f18fd65f08c2ec964023417fa73c7ab98a3ed5a8f82384cef647c396805c1a8244545c879a5f38fe2724f09fe8a2a0046be8cdce398fca60e3aa2698fee1df45a19ee7834fd4beb4aa6d5db4a3bdc5637910b3deefcff20f682a57f4eaedc7d827b000fa15431ca593453f1da86dfe99b53d7ac8eeb8e4ac3b856b033147cabc3a09bccb994f8e02bbb15ef21970333a4e904b1b4db235fb5dbe8ecc09a5126e402b196ad6e8a06067950f34f40f6a804dfbd45434b01fed82ac79d8cb06ec8455e5d0946b00fcc396d7d4a19cb63c59ee9d079e46782628548cdc2aa69e10df1c9df1'\n", "\n", "\n", "BASE_PATH = os.getcwd() \n", "KAGGLE_INPUT_PATH = os.path.join(BASE_PATH, 'input')\n", "KAGGLE_WORKING_PATH = os.path.join(BASE_PATH, 'working')\n", "KAGGLE_SYMLINK = 'kaggle'\n", "\n", "shutil.rmtree(KAGGLE_INPUT_PATH, ignore_errors=True)\n", "os.makedirs(KAGGLE_INPUT_PATH, 0o777, exist_ok=True)\n", "os.makedirs(KAGGLE_WORKING_PATH, 0o777, exist_ok=True)\n", "\n", "try:\n", "    os.symlink(KAGGLE_INPUT_PATH, os.path.join(\"..\", 'input'), target_is_directory=True)\n", "except FileExistsError:\n", "    pass\n", "try:\n", "    os.symlink(KAGGLE_WORKING_PATH, os.path.join(\"..\", 'working'), target_is_directory=True)\n", "except FileExistsError:\n", "    pass\n", "\n", "for data_source_mapping in DATA_SOURCE_MAPPING.split(','):\n", "    directory, download_url_encoded = data_source_mapping.split(':')\n", "    download_url = unquote(download_url_encoded)\n", "    filename = urlparse(download_url).path\n", "    destination_path = os.path.join(KAGGLE_INPUT_PATH, directory)\n", "    try:\n", "        with urlopen(download_url) as fileres, NamedTemporaryFile() as tfile:\n", "            total_length = fileres.headers['content-length']\n", "            print(f'Downloading {directory}, {total_length} bytes compressed')\n", "            dl = 0\n", "            data = fileres.read(CHUNK_SIZE)\n", "            while len(data) > 0:\n", "                dl += len(data)\n", "                tfile.write(data)\n", "                done = int(50 * dl / int(total_length))\n", "                sys.stdout.write(f\"\\r[{'=' * done}{' ' * (50-done)}] {dl} bytes downloaded\")\n", "                sys.stdout.flush()\n", "                data = fileres.read(CHUNK_SIZE)\n", "            if filename.endswith('.zip'):\n", "                with ZipFile(tfile) as zfile:\n", "                    zfile.extractall(destination_path)\n", "            else:\n", "                with tarfile.open(tfile.name) as tarfile:\n", "                    tarfile.extractall(destination_path)\n", "            print(f'\\nDownloaded and uncompressed: {directory}')\n", "    except HTTPError as e:\n", "        print(f'Failed to load (likely expired) {download_url} to path {destination_path}')\n", "        continue\n", "    except OSError as e:\n", "        print(f'Failed to load {download_url} to path {destination_path}')\n", "        continue\n", "\n", "print('Data source import complete.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "collapsed": true, "execution": {"iopub.execute_input": "2024-04-22T15:36:30.444156Z", "iopub.status.busy": "2024-04-22T15:36:30.443821Z", "iopub.status.idle": "2024-04-22T15:37:18.910213Z", "shell.execute_reply": "2024-04-22T15:37:18.908144Z", "shell.execute_reply.started": "2024-04-22T15:36:30.444128Z"}, "id": "u2c65P_1YGbN", "outputId": "1de6e023-c72a-4ea3-c45f-46c6eff680a1", "trusted": true}, "outputs": [], "source": ["%pip install git+https://github.com/coqui-ai/TTS\n", "\n", "# or !pip install git+https://github.com/coqui-ai/TTS"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:37:18.913394Z", "iopub.status.busy": "2024-04-22T15:37:18.912934Z", "iopub.status.idle": "2024-04-22T15:37:18.931371Z", "shell.execute_reply": "2024-04-22T15:37:18.929889Z", "shell.execute_reply.started": "2024-04-22T15:37:18.913352Z"}, "id": "O1E49ChbYGbN", "trusted": true}, "outputs": [], "source": ["import os\n", "import sys\n", "import csv\n", "import shutil\n", "import librosa\n", "import numpy as np\n", "import pandas as pd\n", "from scipy.stats import norm\n", "from tqdm import tqdm_notebook as tqdm\n", "from multiprocessing import Pool\n", "from matplotlib import pylab as plt\n", "from collections import Counter\n", "from TTS.config.shared_configs import BaseDatasetConfig\n", "from TTS.tts.datasets import load_tts_samples\n", "from TTS.tts.datasets.formatters import *\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load Dataset\n", "\n", "Input path to the folder that contains the metadata.csv the wavs folder.\n", "/path/to/your/dataset/\n", "├── metadata.csv\n", "└── wavs/\n", "\n", "Download and let it place the example dataset so you can get the correct folder structure"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:37:18.937201Z", "iopub.status.busy": "2024-04-22T15:37:18.935372Z", "iopub.status.idle": "2024-04-22T15:37:18.960885Z", "shell.execute_reply": "2024-04-22T15:37:18.959394Z", "shell.execute_reply.started": "2024-04-22T15:37:18.937142Z"}, "id": "dTCvk-NYYGbO", "trusted": true}, "outputs": [], "source": ["\n", "training_dir = os.path.join(BASE_PATH, 'input', 'janeeyre') # path to the training data when you switch out. (BASE_PATH, 'PARENT folder', 'dataset folder')\n", "print(f\"Training directory: {training_dir}\") \n", "\n", "NUM_PROC = 4\n", "DATASET_CONFIG = BaseDatasetConfig(\n", "    formatter=\"ljspeech\",\n", "    meta_file_train=\"metadata.csv\",\n", "    path=training_dir,\n", ")\n", "\n", "meta_file_path = os.path.join(training_dir, \"metadata.csv\")\n", "if not os.path.exists(meta_file_path):\n", "    print(f\"File not found: {meta_file_path}\")\n", "else:\n", "    print(f\"File found: {meta_file_path}\")\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:37:18.963526Z", "iopub.status.busy": "2024-04-22T15:37:18.963043Z", "iopub.status.idle": "2024-04-22T15:37:18.974426Z", "shell.execute_reply": "2024-04-22T15:37:18.972983Z", "shell.execute_reply.started": "2024-04-22T15:37:18.963481Z"}, "id": "k97mvHiGYGbO", "trusted": true}, "outputs": [], "source": ["def formatter(root_path, meta_file, ignored_speakers=None):\n", "    txt_file = os.path.join(root_path, meta_file)\n", "    items = []\n", "    speaker_name = \"myspeaker\"\n", "    with open(txt_file, \"r\", encoding=\"utf-8\") as f:\n", "        for line in f:\n", "            print(f\"Processing line: {line.strip()}\") \n", "            cols = line.split(\"|\")\n", "            print(f\"Columns: {cols}\")\n", "            if len(cols) < 2:\n", "                print(f\"Skipping line due to insufficient columns: {line.strip()}\")\n", "                continue\n", "            wav_file = os.path.join(root_path, \"wavs\", cols[0] + \".wav\")\n", "            text = cols[1]\n", "            items.append({\"text\": text, \"audio_file\": wav_file, \"speaker_name\": speaker_name, \"root_path\": root_path})\n", "    return items"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:37:18.976313Z", "iopub.status.busy": "2024-04-22T15:37:18.975958Z", "iopub.status.idle": "2024-04-22T15:37:19.301762Z", "shell.execute_reply": "2024-04-22T15:37:19.3003Z", "shell.execute_reply.started": "2024-04-22T15:37:18.976286Z"}, "id": "1kJ4K1oQYGbO", "outputId": "1ae12d3d-4e9d-4df1-a3d8-59eddbe728ed", "trusted": true}, "outputs": [], "source": ["train_samples, eval_samples = load_tts_samples(\n", "    DATASET_CONFIG,\n", "    eval_split=True,\n", "    formatter=formatter,\n", ")\n", "if eval_samples is not None:\n", "    items = train_samples + eval_samples\n", "else:\n", "    items = train_samples"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:37:19.304106Z", "iopub.status.busy": "2024-04-22T15:37:19.303607Z", "iopub.status.idle": "2024-04-22T15:37:19.311365Z", "shell.execute_reply": "2024-04-22T15:37:19.309772Z", "shell.execute_reply.started": "2024-04-22T15:37:19.304073Z"}, "id": "Wy77kazaYGbO", "outputId": "9d51b629-2bde-468a-e48b-3efe9eea08ee", "trusted": true}, "outputs": [], "source": ["# How many audio files are there in the dataset?\n", "print(\" > Number of audio files: {}\".format(len(items)))\n", "print(items[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:37:19.324514Z", "iopub.status.busy": "2024-04-22T15:37:19.324058Z", "iopub.status.idle": "2024-04-22T15:37:24.787905Z", "shell.execute_reply": "2024-04-22T15:37:24.786631Z", "shell.execute_reply.started": "2024-04-22T15:37:19.324464Z"}, "id": "m0XmgIeOYGbO", "trusted": true}, "outputs": [], "source": ["# print out names of missing wavs\n", "wav_files = []\n", "for item in items:\n", "    wav_file = item[\"audio_file\"].strip()\n", "    wav_files.append(wav_file)\n", "    if not os.path.exists(wav_file):\n", "        print(wav_file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:37:24.790477Z", "iopub.status.busy": "2024-04-22T15:37:24.789884Z", "iopub.status.idle": "2024-04-22T15:37:24.80202Z", "shell.execute_reply": "2024-04-22T15:37:24.800905Z", "shell.execute_reply.started": "2024-04-22T15:37:24.790432Z"}, "id": "QfgY6hn5YGbP", "outputId": "dd7751e7-1c92-4c5c-9992-7ee31b6f96ec", "trusted": true}, "outputs": [], "source": ["# print out names of duplicate wavs\n", "c = Counter(wav_files)\n", "print([item for item, count in c.items() if count > 1])"]}, {"cell_type": "markdown", "metadata": {"id": "gAP44a_dYGbP"}, "source": ["### Compute Audio/Text Lengths"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:37:24.804338Z", "iopub.status.busy": "2024-04-22T15:37:24.80358Z", "iopub.status.idle": "2024-04-22T15:37:58.131623Z", "shell.execute_reply": "2024-04-22T15:37:58.127975Z", "shell.execute_reply.started": "2024-04-22T15:37:24.804303Z"}, "id": "Vks2xUkxYGbP", "outputId": "31ca89d4-c682-49b0-c9f3-e86a155c7e48", "trusted": true}, "outputs": [], "source": ["\n", "def load_item(item):\n", "    text = item[\"text\"].strip()\n", "    file_name = item[\"audio_file\"].strip()\n", "    audio, sr = librosa.load(file_name, sr=None)\n", "    audio_len = len(audio) / sr\n", "    text_len = len(text)\n", "    return file_name, text, text_len, audio, audio_len\n", "\n", "# This will take a while depending on size of dataset\n", "if NUM_PROC == 1:\n", "    data = []\n", "    for m in tqdm(items):\n", "        data += [load_item(m)]\n", "else:\n", "    with Pool(NUM_PROC) as p:\n", "        data = list(tqdm(p.imap(load_item, items), total=len(items)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:37:58.135357Z", "iopub.status.busy": "2024-04-22T15:37:58.134679Z", "iopub.status.idle": "2024-04-22T15:37:58.282561Z", "shell.execute_reply": "2024-04-22T15:37:58.281198Z", "shell.execute_reply.started": "2024-04-22T15:37:58.135312Z"}, "id": "DQPFqNRTYGbP", "outputId": "b1d2765d-3b8a-41b9-b4c0-d1c2cec816ec", "trusted": true}, "outputs": [], "source": ["# look at breadth of vocabulary\n", "w_count = Counter()\n", "for item in tqdm(data):\n", "    text = item[1].lower().strip()\n", "    for word in text.split():\n", "        w_count[word] += 1\n", "print(\" > Number of words: {}\".format(len(w_count)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:37:58.284674Z", "iopub.status.busy": "2024-04-22T15:37:58.284311Z", "iopub.status.idle": "2024-04-22T15:37:58.329275Z", "shell.execute_reply": "2024-04-22T15:37:58.327893Z", "shell.execute_reply.started": "2024-04-22T15:37:58.284645Z"}, "id": "5CJ8pN4pYGbP", "outputId": "de42c139-6968-4c44-ccb2-8f806e2b4a3b", "trusted": true}, "outputs": [], "source": ["#length of text, duration of audio in seconds and compare\n", "text_vs_durs = {}  # text length vs audio duration\n", "text_len_counter = Counter()  # number of sentences with the keyed length\n", "lengths = []\n", "for item in tqdm(data):\n", "    text = item[1].lower().strip()\n", "    text_len = len(text)\n", "    text_len_counter[text_len] += 1\n", "    lengths.append(text_len)\n", "    audio_len = item[-1]\n", "    try:\n", "        text_vs_durs[text_len] += [audio_len]\n", "    except:\n", "        text_vs_durs[text_len] = [audio_len]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:37:58.331174Z", "iopub.status.busy": "2024-04-22T15:37:58.330832Z", "iopub.status.idle": "2024-04-22T15:37:58.363503Z", "shell.execute_reply": "2024-04-22T15:37:58.362238Z", "shell.execute_reply.started": "2024-04-22T15:37:58.331145Z"}, "id": "1B9PthnlYGbP", "trusted": true}, "outputs": [], "source": ["#for each bucket of char lengths, find averages for that length vs duration of audio\n", "text_vs_avg = {}\n", "text_vs_median = {}\n", "text_vs_std = {}\n", "for key, durs in text_vs_durs.items():\n", "    text_vs_avg[key] = np.mean(durs)\n", "    text_vs_median[key] = np.median(durs)\n", "    text_vs_std[key] = np.std(durs)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:37:58.365279Z", "iopub.status.busy": "2024-04-22T15:37:58.364923Z", "iopub.status.idle": "2024-04-22T15:37:58.383115Z", "shell.execute_reply": "2024-04-22T15:37:58.381752Z", "shell.execute_reply.started": "2024-04-22T15:37:58.36525Z"}, "id": "XUp26YNMYGbP", "outputId": "f55d7919-3eb6-45b9-893b-fd44ae3a3724", "trusted": true}, "outputs": [], "source": ["#per char duration\n", "sec_per_chars = []\n", "for item in data:\n", "    text = item[1]\n", "    dur = item[-1]\n", "    sec_per_char = dur / len(text)\n", "    sec_per_chars.append(sec_per_char)\n", "\n", "mean = np.mean(sec_per_chars)\n", "std = np.std(sec_per_chars)\n", "print(mean)\n", "print(std)"]}, {"cell_type": "markdown", "metadata": {"id": "vu1Z3hs5YGbP"}, "source": ["### Visualise Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 488}, "execution": {"iopub.execute_input": "2024-04-22T15:37:58.385434Z", "iopub.status.busy": "2024-04-22T15:37:58.384956Z", "iopub.status.idle": "2024-04-22T15:37:58.756753Z", "shell.execute_reply": "2024-04-22T15:37:58.755502Z", "shell.execute_reply.started": "2024-04-22T15:37:58.385374Z"}, "id": "Kw8jR0AnYGbP", "outputId": "3bbb51b4-8afd-49c3-ae1d-b4dda2d8b297", "trusted": true}, "outputs": [], "source": ["plt.title(\"text length vs mean audio duration\")\n", "plt.scatter(list(text_vs_avg.keys()), list(text_vs_avg.values()))"]}, {"cell_type": "markdown", "metadata": {"id": "NQBKdXRGYGbP"}, "source": ["Note that \"text length\" is amount of chars. Audio duration should basically be a linear function of text length."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 488}, "execution": {"iopub.execute_input": "2024-04-22T15:37:58.759577Z", "iopub.status.busy": "2024-04-22T15:37:58.758513Z", "iopub.status.idle": "2024-04-22T15:37:59.144729Z", "shell.execute_reply": "2024-04-22T15:37:59.143465Z", "shell.execute_reply.started": "2024-04-22T15:37:58.759543Z"}, "id": "CyI4yi5MYGbP", "outputId": "f74cf873-c6b3-453e-c6a3-4908be67b0a4", "trusted": true}, "outputs": [], "source": ["plt.title(\"text length vs STD\")\n", "plt.scatter(list(text_vs_std.keys()), list(text_vs_std.values()))"]}, {"cell_type": "markdown", "metadata": {"id": "B4Z7WnTxYGbQ"}, "source": ["This is showing us, for each set of data items with a particular char length, what is one standard deviation in audio duration for that set."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 489}, "execution": {"iopub.execute_input": "2024-04-22T15:37:59.146775Z", "iopub.status.busy": "2024-04-22T15:37:59.146307Z", "iopub.status.idle": "2024-04-22T15:37:59.536863Z", "shell.execute_reply": "2024-04-22T15:37:59.535491Z", "shell.execute_reply.started": "2024-04-22T15:37:59.146742Z"}, "id": "unY18k-OYGbQ", "outputId": "b668478e-8b9e-42b5-e6fa-fd9c219eca7f", "trusted": true}, "outputs": [], "source": ["plt.hist(lengths, bins=20, edgecolor='black')  # Adjust the number of bins as needed\n", "plt.xlabel('Text Length')\n", "plt.ylabel('Frequency')\n", "plt.title('Distribution of Text Lengths in TTS Dataset')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "iFk3y6JgYGbQ"}, "source": ["The TTS docs recommends a 'Gaussian-like' distribution of clip lengths. A fairly uniform distribution should be fine too. I think the most important thing is to have examples across a range of different lengths. From my experience, the model does not generalise well across different sequence lengths (i.e., if you fine-tune with no short 1.5s clips and then try to create such a short clip at inference time, then it will probably struggle)."]}, {"cell_type": "markdown", "metadata": {"id": "H4Ul3pLEYGbQ"}, "source": ["### Clean the Dataset\n", "\n", "Now we will apply some filters to remove items from our dataset that are likely to be problematic"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:37:59.539661Z", "iopub.status.busy": "2024-04-22T15:37:59.539092Z", "iopub.status.idle": "2024-04-22T15:37:59.581778Z", "shell.execute_reply": "2024-04-22T15:37:59.580276Z", "shell.execute_reply.started": "2024-04-22T15:37:59.539615Z"}, "id": "Weh6vQfhYGbQ", "outputId": "3ab98e11-fe1f-4aee-cdf4-46f8396d317f", "trusted": true}, "outputs": [], "source": ["cleaned_data=[]\n", "len(data), len(cleaned_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:37:59.584495Z", "iopub.status.busy": "2024-04-22T15:37:59.583959Z", "iopub.status.idle": "2024-04-22T15:37:59.602214Z", "shell.execute_reply": "2024-04-22T15:37:59.600925Z", "shell.execute_reply.started": "2024-04-22T15:37:59.584453Z"}, "id": "l7FG1tUJYGbQ", "outputId": "70fdc743-f7e2-43b5-a159-a69f3a09f800", "trusted": true}, "outputs": [], "source": ["#first find our per char duration mean/sd\n", "durs_per_char = []\n", "for each in data:\n", "    durs_per_char.append(each[-1]/each[2])\n", "durs_mean = np.mean(durs_per_char)\n", "durs_sd = np.std(durs_per_char)\n", "\n", "durs_mean, durs_sd"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 735}, "execution": {"iopub.execute_input": "2024-04-22T15:37:59.603835Z", "iopub.status.busy": "2024-04-22T15:37:59.603506Z", "iopub.status.idle": "2024-04-22T15:38:00.136532Z", "shell.execute_reply": "2024-04-22T15:38:00.135223Z", "shell.execute_reply.started": "2024-04-22T15:37:59.603808Z"}, "id": "aodmSDdwYGbQ", "outputId": "1d52173d-3dff-40f1-8646-0af336f14447", "trusted": true}, "outputs": [], "source": ["points = durs_per_char\n", "mean = durs_mean\n", "std_dev = durs_sd\n", "\n", "plt.figure(figsize=(10, 8))\n", "plt.hist(points, bins=30, edgecolor='black', linewidth=1.2)\n", "\n", "# Add vertical lines for mean and standard deviation\n", "plt.axvline(mean, color='white', linestyle='dashed', linewidth=1.5, label=f'Mean: {mean:.5f}')\n", "plt.axvline(mean + std_dev, color='orange', linestyle='dashed', linewidth=1.5, label=f'Standard Deviation: {std_dev:.5f}')\n", "plt.axvline(mean - std_dev, color='orange', linestyle='dashed', linewidth=1.5)\n", "plt.axvline(mean + 2*std_dev, color='red', linestyle='dashed', linewidth=1.5, label=f'Standard Deviation: {std_dev:.5f}')\n", "plt.axvline(mean - 2*std_dev, color='red', linestyle='dashed', linewidth=1.5)\n", "\n", "# Add legend\n", "plt.legend()\n", "\n", "# Add labels and title\n", "plt.xlabel('Value')\n", "plt.ylabel('Frequency')\n", "plt.title('Speech Duration Per Character for All Items')\n", "\n", "# Show plot\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "i1jnP3bHYGbQ"}, "source": ["We have found the mean duration per character of text and the above histogram shows how our dataset is distributed around that mean.\n", "\n", "Next we will filter out items with too short duration, items with too long duration, and items with too much variance"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:38:00.139899Z", "iopub.status.busy": "2024-04-22T15:38:00.13875Z", "iopub.status.idle": "2024-04-22T15:38:00.145833Z", "shell.execute_reply": "2024-04-22T15:38:00.144555Z", "shell.execute_reply.started": "2024-04-22T15:38:00.139852Z"}, "id": "E2ZxkUqRYGbQ", "trusted": true}, "outputs": [], "source": ["minimum_duration = 0.7\n", "maximum_duration = 13.0\n", "maximum_sds = 2.5"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:38:00.14875Z", "iopub.status.busy": "2024-04-22T15:38:00.14822Z", "iopub.status.idle": "2024-04-22T15:38:00.196271Z", "shell.execute_reply": "2024-04-22T15:38:00.194138Z", "shell.execute_reply.started": "2024-04-22T15:38:00.148708Z"}, "id": "SnCCD-vHYGbQ", "outputId": "a1c794fe-1a23-4576-d251-19938db062ff", "trusted": true}, "outputs": [], "source": ["#TODO: add separate filter for removing items whose char length is > 250\n", "\n", "cleaned_data = []\n", "\n", "shorties = []\n", "longies = []\n", "misfits = []\n", "for item in data:\n", "    item_perchar_dur = item[-1]/item[2]\n", "    difference = abs(item_perchar_dur-durs_mean)\n", "    item_zscore = difference / durs_sd\n", "    item = item + (item_zscore,) #add the zscore to the data item so we can sort by it later\n", "    if item[-2] < minimum_duration:\n", "        shorties.append(item)\n", "    elif item[-2] > maximum_duration:\n", "        longies.append(item)\n", "    elif item_zscore > maximum_sds:\n", "        misfits.append(item)\n", "    else:\n", "        cleaned_data.append(item)\n", "\n", "excluded = shorties + longies + misfits\n", "\n", "\n", "print(f\"found {len(shorties)} short items and {len(longies)} long items and {len(misfits)} items whose length conformed but whose per-char duration exceeded {maximum_sds} standard deviations from the mean. Excluding {len(shorties)+len(longies)+len(misfits)} items\")"]}, {"cell_type": "markdown", "metadata": {"id": "CyD7CXdQYGbQ"}, "source": ["Look at the items we are deeming too short to see if it looks sensible to exclude them"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:38:00.198618Z", "iopub.status.busy": "2024-04-22T15:38:00.197923Z", "iopub.status.idle": "2024-04-22T15:38:00.206563Z", "shell.execute_reply": "2024-04-22T15:38:00.205197Z", "shell.execute_reply.started": "2024-04-22T15:38:00.198579Z"}, "id": "V4-qg7wLYGbQ", "outputId": "605fecae-fb3b-4d7a-e95a-98a50025f24a", "trusted": true}, "outputs": [], "source": ["ranked_shorts = sorted(shorties, key=lambda x: x[-2])\n", "if len(ranked_shorts) > 0:\n", "    print(f\"Duration of shortest item excluded for being too short: {ranked_shorts[0][-2]} Text from shortest item excluded for being too short: {ranked_shorts[0][1]}\")\n", "    print(ranked_shorts[0])\n", "    print(f\"Duration of longest item excluded for being too short: {ranked_shorts[-1][-2]} Text from shortest item excluded for being too short: {ranked_shorts[-1][1]}\")\n", "    print(ranked_shorts[-1])\n"]}, {"cell_type": "markdown", "metadata": {"id": "LLC0h4stYGbR"}, "source": ["Likewise for the items with high variance (you might want to play around with the maximum_sds value above to find the right level of exclusion)\n", "\n", "(If you are following along in the jane<PERSON>re dataset, this actually picks up some items from the dataset that were mislabelled)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:38:00.208101Z", "iopub.status.busy": "2024-04-22T15:38:00.207734Z", "iopub.status.idle": "2024-04-22T15:38:00.406547Z", "shell.execute_reply": "2024-04-22T15:38:00.405037Z", "shell.execute_reply.started": "2024-04-22T15:38:00.20807Z"}, "id": "jERQWUhBYGbR", "outputId": "d7e589f0-6f10-4a2e-d8bb-6ab4ee11fdf9", "trusted": true}, "outputs": [], "source": ["ranked_misfits = sorted(misfits, key=lambda x: x[-1])[::-1]\n", "if len(ranked_misfits) > 0:\n", "    print(f\"Duration of worst item excluded for having too much variance: {ranked_misfits[0][-2]} and its text: {ranked_misfits[0][1]}\")\n", "    print(ranked_misfits[0])\n", "    print(f\"Duration of best item excluded for having too much variance: {ranked_misfits[-1][-2]} and its text: {ranked_misfits[-1][1]}\")\n", "    print(ranked_misfits[-1])\n"]}, {"cell_type": "markdown", "metadata": {"id": "-6SnHaY7YGbR"}, "source": ["The following code is copy pasted from above--we are recomputing on our cleaned dataset to inspect it and compare"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:38:00.408919Z", "iopub.status.busy": "2024-04-22T15:38:00.408355Z", "iopub.status.idle": "2024-04-22T15:38:00.454898Z", "shell.execute_reply": "2024-04-22T15:38:00.45361Z", "shell.execute_reply.started": "2024-04-22T15:38:00.408885Z"}, "id": "-maBQargYGbR", "outputId": "4a91e78a-313d-452d-9541-b61f04a661eb", "trusted": true}, "outputs": [], "source": ["\n", "text_vs_durs = {}  # text length vs audio duration\n", "text_len_counter = Counter()  # number of sentences with the keyed length\n", "lengths = []\n", "for item in tqdm(cleaned_data):\n", "    text = item[1].lower().strip()\n", "    text_len = len(text)\n", "    text_len_counter[text_len] += 1\n", "    lengths.append(text_len)\n", "    audio_len = item[-2]\n", "    try:\n", "        text_vs_durs[text_len] += [audio_len]\n", "    except:\n", "        text_vs_durs[text_len] = [audio_len]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:38:00.456956Z", "iopub.status.busy": "2024-04-22T15:38:00.456562Z", "iopub.status.idle": "2024-04-22T15:38:00.489132Z", "shell.execute_reply": "2024-04-22T15:38:00.487828Z", "shell.execute_reply.started": "2024-04-22T15:38:00.456922Z"}, "id": "vLU6AOy-YGbR", "trusted": true}, "outputs": [], "source": ["# text_len vs avg_audio_len, median_audio_len, std_audio_len\n", "text_vs_avg = {}\n", "text_vs_median = {}\n", "text_vs_std = {}\n", "for key, durs in text_vs_durs.items():\n", "    text_vs_avg[key] = np.mean(durs)\n", "    text_vs_median[key] = np.median(durs)\n", "    text_vs_std[key] = np.std(durs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:38:00.494043Z", "iopub.status.busy": "2024-04-22T15:38:00.493636Z", "iopub.status.idle": "2024-04-22T15:38:00.508448Z", "shell.execute_reply": "2024-04-22T15:38:00.507249Z", "shell.execute_reply.started": "2024-04-22T15:38:00.49401Z"}, "id": "dFUyRK7fYGbR", "trusted": true}, "outputs": [], "source": ["sec_per_chars = []\n", "for item in cleaned_data:\n", "    text = item[1]\n", "    dur = item[-2]\n", "    sec_per_char = dur / len(text)\n", "    sec_per_chars.append(sec_per_char)\n", "# sec_per_char /= len(cleaned_data)\n", "# print(sec_per_char)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:38:00.510342Z", "iopub.status.busy": "2024-04-22T15:38:00.509999Z", "iopub.status.idle": "2024-04-22T15:38:00.52163Z", "shell.execute_reply": "2024-04-22T15:38:00.520371Z", "shell.execute_reply.started": "2024-04-22T15:38:00.510313Z"}, "id": "el0XRZgbYGbV", "outputId": "72f256b8-e104-479f-d1c5-9a90e36fbcd3", "trusted": true}, "outputs": [], "source": ["mean = np.mean(sec_per_chars)\n", "std = np.std(sec_per_chars)\n", "print(mean)\n", "print(std)"]}, {"cell_type": "markdown", "metadata": {"id": "XxO7seTJYGbV"}, "source": ["We should see less outliers in the visualisations below and a tighter distribution of audio duration per char"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 488}, "execution": {"iopub.execute_input": "2024-04-22T15:38:00.524019Z", "iopub.status.busy": "2024-04-22T15:38:00.523547Z", "iopub.status.idle": "2024-04-22T15:38:00.872971Z", "shell.execute_reply": "2024-04-22T15:38:00.871675Z", "shell.execute_reply.started": "2024-04-22T15:38:00.523968Z"}, "id": "PVi-IUL_YGbV", "outputId": "c8a2126e-2a82-4103-8571-ac3d724804dc", "trusted": true}, "outputs": [], "source": ["plt.title(\"text length vs mean audio duration\")\n", "plt.scatter(list(text_vs_avg.keys()), list(text_vs_avg.values()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 488}, "execution": {"iopub.execute_input": "2024-04-22T15:38:00.875074Z", "iopub.status.busy": "2024-04-22T15:38:00.874549Z", "iopub.status.idle": "2024-04-22T15:38:01.228276Z", "shell.execute_reply": "2024-04-22T15:38:01.22691Z", "shell.execute_reply.started": "2024-04-22T15:38:00.874969Z"}, "id": "oJbbJoKfYGbV", "outputId": "3395288a-6fbd-474f-b84a-18b0394fd2b1", "trusted": true}, "outputs": [], "source": ["plt.title(\"text length vs median audio duration\")\n", "plt.scatter(list(text_vs_median.keys()), list(text_vs_median.values()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 488}, "execution": {"iopub.execute_input": "2024-04-22T15:38:01.230528Z", "iopub.status.busy": "2024-04-22T15:38:01.23008Z", "iopub.status.idle": "2024-04-22T15:38:01.585275Z", "shell.execute_reply": "2024-04-22T15:38:01.584146Z", "shell.execute_reply.started": "2024-04-22T15:38:01.230492Z"}, "id": "G620Ub1DYGbV", "outputId": "66a3f5ab-b814-4424-da6c-16c3a36c9524", "trusted": true}, "outputs": [], "source": ["plt.title(\"text length vs STD\")\n", "plt.scatter(list(text_vs_std.keys()), list(text_vs_std.values()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 489}, "execution": {"iopub.execute_input": "2024-04-22T15:38:01.587908Z", "iopub.status.busy": "2024-04-22T15:38:01.587441Z", "iopub.status.idle": "2024-04-22T15:38:01.96433Z", "shell.execute_reply": "2024-04-22T15:38:01.963025Z", "shell.execute_reply.started": "2024-04-22T15:38:01.587864Z"}, "id": "xQIH7noHYGbV", "outputId": "9399bc53-0982-4abf-c10f-a24441be4f39", "trusted": true}, "outputs": [], "source": ["plt.hist(lengths, bins=20, edgecolor='black')  # Adjust the number of bins as needed\n", "plt.xlabel('Text Length')\n", "plt.ylabel('Frequency')\n", "plt.title('Distribution of Text Lengths in TTS Dataset')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:38:01.969794Z", "iopub.status.busy": "2024-04-22T15:38:01.968449Z", "iopub.status.idle": "2024-04-22T15:38:01.984928Z", "shell.execute_reply": "2024-04-22T15:38:01.983519Z", "shell.execute_reply.started": "2024-04-22T15:38:01.969741Z"}, "id": "fLZoixY4YGbV", "outputId": "df2be95f-a8e2-47e6-e1b3-def5f28da82b", "trusted": true}, "outputs": [], "source": ["#after cleaning distribution\n", "#first find our per char duration mean/sd\n", "durs_per_char = []\n", "for each in cleaned_data:\n", "    durs_per_char.append(each[-2]/each[2])\n", "durs_mean = np.mean(durs_per_char)\n", "durs_sd = np.std(durs_per_char)\n", "\n", "durs_mean, durs_sd"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 735}, "execution": {"iopub.execute_input": "2024-04-22T15:38:01.986947Z", "iopub.status.busy": "2024-04-22T15:38:01.986538Z", "iopub.status.idle": "2024-04-22T15:38:02.509887Z", "shell.execute_reply": "2024-04-22T15:38:02.508537Z", "shell.execute_reply.started": "2024-04-22T15:38:01.986912Z"}, "id": "QRiGsNlTYGbV", "outputId": "bdfc0a8d-4c73-4969-af28-fe31a53edd09", "trusted": true}, "outputs": [], "source": ["points = durs_per_char\n", "mean = durs_mean\n", "std_dev = durs_sd\n", "\n", "plt.figure(figsize=(10, 8))\n", "plt.hist(points, bins=30, edgecolor='black', linewidth=1.2)\n", "\n", "# Add vertical lines for mean and standard deviation\n", "plt.axvline(mean, color='white', linestyle='dashed', linewidth=1.5, label=f'Mean: {mean:.5f}')\n", "plt.axvline(mean + std_dev, color='orange', linestyle='dashed', linewidth=1.5, label=f'Standard Deviation: {std_dev:.5f}')\n", "plt.axvline(mean - std_dev, color='orange', linestyle='dashed', linewidth=1.5)\n", "plt.axvline(mean + 2*std_dev, color='red', linestyle='dashed', linewidth=1.5, label=f'Standard Deviation: {std_dev:.5f}')\n", "plt.axvline(mean - 2*std_dev, color='red', linestyle='dashed', linewidth=1.5)\n", "\n", "# Add legend\n", "plt.legend()\n", "\n", "# Add labels and title\n", "plt.xlabel('Value')\n", "plt.ylabel('Frequency')\n", "plt.title('Speech Duration Per Character for All Items')\n", "\n", "# Show plot\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "BdBdz5IJYGbV"}, "source": ["### Visualise Cull\n", "\n", "Check how much content we are proposing to remove from our dataset. I don't know how much data is 'enough' to train on. In my experience, 2 hours is plenty of audio duration (I trained a model on 10 hours of audio and a 2 hour subset of that data and I couldn't notice much difference between the two). The more high quality content, the better.\n", "\n", "If you are having to cut a large amount of data at this stage, then your dataset probably isn't being created correctly to begin with. [here](https://github.com/zuverschenken/XTTSv2Scripts) is my repo showing how to create a dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:40:29.34804Z", "iopub.status.busy": "2024-04-22T15:40:29.347564Z", "iopub.status.idle": "2024-04-22T15:40:29.357163Z", "shell.execute_reply": "2024-04-22T15:40:29.355515Z", "shell.execute_reply.started": "2024-04-22T15:40:29.348006Z"}, "id": "8IfPGNXsYGbW", "outputId": "e073eac7-bf83-4299-f998-6592b6bf1914", "trusted": true}, "outputs": [], "source": ["len(excluded)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:40:30.130778Z", "iopub.status.busy": "2024-04-22T15:40:30.130336Z", "iopub.status.idle": "2024-04-22T15:40:30.139303Z", "shell.execute_reply": "2024-04-22T15:40:30.138251Z", "shell.execute_reply.started": "2024-04-22T15:40:30.130746Z"}, "id": "NgTuY5nUYGbW", "outputId": "164b1f82-8b7e-47f6-b081-ec2887d82d9a", "trusted": true}, "outputs": [], "source": ["len(data), len(cleaned_data), len(cleaned_data)/len(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:40:36.652114Z", "iopub.status.busy": "2024-04-22T15:40:36.651674Z", "iopub.status.idle": "2024-04-22T15:40:36.662073Z", "shell.execute_reply": "2024-04-22T15:40:36.660891Z", "shell.execute_reply.started": "2024-04-22T15:40:36.652081Z"}, "id": "PslzkRB2YGbW", "outputId": "246ea422-9426-445b-ec1e-775fa9424f8f", "trusted": true}, "outputs": [], "source": ["remaining_length = sum([each[-2] for each in cleaned_data])\n", "excluded_length = sum([each[-2] for each in excluded])\n", "remaining_length, excluded_length"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 538}, "execution": {"iopub.execute_input": "2024-04-22T15:40:40.977914Z", "iopub.status.busy": "2024-04-22T15:40:40.977504Z", "iopub.status.idle": "2024-04-22T15:40:41.171528Z", "shell.execute_reply": "2024-04-22T15:40:41.169717Z", "shell.execute_reply.started": "2024-04-22T15:40:40.977883Z"}, "id": "xvNfamniYGbW", "outputId": "a59318d7-870e-4ea3-d8b4-75d39836696b", "trusted": true}, "outputs": [], "source": ["\n", "# Data for the pie chart (proportions)\n", "sizes = [remaining_length, excluded_length]  # Example proportions, summing up to 100%\n", "\n", "# Labels for each portion\n", "labels = ['Remaining Data', 'Excluded Data']\n", "\n", "# Colors for each portion\n", "colors = ['lightblue', 'lightcoral']  # Adjusted color for 'lightred'\n", "\n", "# Create pie chart\n", "plt.figure(figsize=(6, 6))\n", "plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=140)\n", "plt.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle\n", "plt.title('Proportion of seconds of audio kept after cull')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:41:06.243308Z", "iopub.status.busy": "2024-04-22T15:41:06.242871Z", "iopub.status.idle": "2024-04-22T15:41:06.251073Z", "shell.execute_reply": "2024-04-22T15:41:06.249777Z", "shell.execute_reply.started": "2024-04-22T15:41:06.243274Z"}, "id": "ShQ9bSbiYGbW", "outputId": "52f15f3f-6406-4730-967f-f7ee690d7f66", "trusted": true}, "outputs": [], "source": ["def seconds_to_hms(seconds):\n", "    hours = seconds // 3600\n", "    minutes = (seconds % 3600) // 60\n", "    remaining_seconds = seconds % 60\n", "    return hours, minutes, remaining_seconds\n", "\n", "hours, minutes, remaining_seconds = seconds_to_hms(remaining_length)\n", "print(f\"Remaining length: {hours} hours, {minutes} minutes, and {remaining_seconds} seconds.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:41:09.741031Z", "iopub.status.busy": "2024-04-22T15:41:09.740363Z", "iopub.status.idle": "2024-04-22T15:41:09.747331Z", "shell.execute_reply": "2024-04-22T15:41:09.745983Z", "shell.execute_reply.started": "2024-04-22T15:41:09.740996Z"}, "id": "adCufsDTYGbW", "outputId": "f5e350f8-b158-4a8e-d64c-9832b6924934", "trusted": true}, "outputs": [], "source": ["hours, minutes, remaining_seconds = seconds_to_hms(excluded_length)\n", "print(f\"Excluded length: {hours} hours, {minutes} minutes, and {remaining_seconds} seconds.\")"]}, {"cell_type": "markdown", "metadata": {"id": "5ThFAM-CYGbW"}, "source": ["### Check Parameter for Fine-tune\n", "\n", "If you are using this data with XTTS-v2, you should use the maximum audio length from your dataset as max_wav_length and ensure the length of the reference audio you want to use is within the min-max range."]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:51:09.33224Z", "iopub.status.busy": "2024-04-22T15:51:09.330317Z", "iopub.status.idle": "2024-04-22T15:51:09.478877Z", "shell.execute_reply": "2024-04-22T15:51:09.477644Z", "shell.execute_reply.started": "2024-04-22T15:51:09.332178Z"}, "id": "E6Fwu4ovYGbW", "trusted": true}, "outputs": [], "source": ["speaker_reference = '/input/trmp/trumpbagdaddy.wav'\n", "audio, sr = librosa.load(os.path.join(training_dir, 'wavs', speaker_reference), sr=None)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:51:37.507708Z", "iopub.status.busy": "2024-04-22T15:51:37.507213Z", "iopub.status.idle": "2024-04-22T15:51:37.52084Z", "shell.execute_reply": "2024-04-22T15:51:37.519053Z", "shell.execute_reply.started": "2024-04-22T15:51:37.507671Z"}, "id": "89tmKBMIYGbW", "outputId": "8ab958a5-02eb-4814-e285-5350408f810d", "trusted": true}, "outputs": [], "source": ["rawLength = [x[-2] for x in cleaned_data]\n", "\n", "print(f\"seconds: {max(rawLength)}\")\n", "print(f\"maximum audio file length: {max(rawLength)*sr}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:54:00.049Z", "iopub.status.busy": "2024-04-22T15:54:00.047703Z", "iopub.status.idle": "2024-04-22T15:54:00.058879Z", "shell.execute_reply": "2024-04-22T15:54:00.056609Z", "shell.execute_reply.started": "2024-04-22T15:54:00.048826Z"}, "id": "Z1on6coxYGbX", "outputId": "0f69f7c0-456b-48ad-bd63-84afe0988928", "trusted": true}, "outputs": [], "source": ["print(f\"speaker reference file length: {len(audio)}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## No need to continue past this point with the example dataset."]}, {"cell_type": "markdown", "metadata": {"id": "IvuklZwOYGbX"}, "source": ["### Delete Unwanted Files.\n", "\n", "Finally we copy across the files we want to keep to /kaggle/working"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:55:14.04474Z", "iopub.status.busy": "2024-04-22T15:55:14.043245Z", "iopub.status.idle": "2024-04-22T15:55:14.049697Z", "shell.execute_reply": "2024-04-22T15:55:14.048655Z", "shell.execute_reply.started": "2024-04-22T15:55:14.044683Z"}, "id": "C0kKn4j0YGbX", "trusted": true}, "outputs": [], "source": ["out_dir = '/workspace/output/' # choose an output directory"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:55:19.238474Z", "iopub.status.busy": "2024-04-22T15:55:19.237977Z", "iopub.status.idle": "2024-04-22T15:55:19.246508Z", "shell.execute_reply": "2024-04-22T15:55:19.245037Z", "shell.execute_reply.started": "2024-04-22T15:55:19.238426Z"}, "id": "JYIfYCGWYGbX", "trusted": true}, "outputs": [], "source": ["excluded_files = set([each[0].split('/')[-1].split('.')[0] for each in excluded])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:55:28.472457Z", "iopub.status.busy": "2024-04-22T15:55:28.472025Z", "iopub.status.idle": "2024-04-22T15:55:28.527929Z", "shell.execute_reply": "2024-04-22T15:55:28.526592Z", "shell.execute_reply.started": "2024-04-22T15:55:28.472423Z"}, "id": "AaXH7HtOYGbX", "trusted": true}, "outputs": [], "source": ["#grab the rows we will keep\n", "dropped = 0\n", "sanitised_rows = []\n", "with open(os.path.join(training_dir, 'metadata.csv'), 'r') as file:\n", "    csv_reader = csv.reader(file, delimiter='|')\n", "    for row in csv_reader:\n", "        if row[0] not in excluded_files:\n", "            sanitised_rows.append(row)\n", "        else:\n", "            dropped += 1\n", "\n", "#sanity check\n", "assert dropped == len(excluded_files)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:55:30.789619Z", "iopub.status.busy": "2024-04-22T15:55:30.789117Z", "iopub.status.idle": "2024-04-22T15:55:30.923929Z", "shell.execute_reply": "2024-04-22T15:55:30.922555Z", "shell.execute_reply.started": "2024-04-22T15:55:30.789582Z"}, "id": "NaekET6bYGbX", "trusted": true}, "outputs": [], "source": ["#create a new csv\n", "with open(os.path.join(out_dir, 'metadata.csv'), 'w', encoding='utf-8') as file:\n", "    csv_writer = csv.writer(file, delimiter = '|')\n", "    for row in sanitised_rows:\n", "        csv_writer.writerow(row)"]}, {"cell_type": "markdown", "metadata": {"id": "wNmtBFYzYGbX"}, "source": ["**Note: copying all wavs to working dir. This might take some time**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2024-04-22T15:55:36.883722Z", "iopub.status.busy": "2024-04-22T15:55:36.883285Z", "iopub.status.idle": "2024-04-22T15:56:09.832136Z", "shell.execute_reply": "2024-04-22T15:56:09.830831Z", "shell.execute_reply.started": "2024-04-22T15:55:36.883689Z"}, "id": "itS5NcCBYGbX", "trusted": true}, "outputs": [], "source": ["wavs_dir = os.path.join(out_dir, 'wavs')\n", "os.makedirs(wavs_dir, exist_ok=True)\n", "\n", "files = os.listdir(os.path.join(training_dir, 'wavs'))\n", "\n", "for file in files:\n", "    source_file = os.path.join(training_dir,'wavs', file)\n", "    destination_file = os.path.join(wavs_dir, file)\n", "    shutil.copy2(source_file, destination_file)\n"]}, {"cell_type": "markdown", "metadata": {"id": "_GvVPh85YGbY"}, "source": ["Delete the unwanted wavs"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:56:50.913209Z", "iopub.status.busy": "2024-04-22T15:56:50.912723Z", "iopub.status.idle": "2024-04-22T15:56:50.952356Z", "shell.execute_reply": "2024-04-22T15:56:50.950476Z", "shell.execute_reply.started": "2024-04-22T15:56:50.913177Z"}, "id": "C06Qz8iaYGbY", "outputId": "db7102e1-f546-4dee-c8cd-14ad9606ff3e", "trusted": true}, "outputs": [], "source": ["#delete wavs\n", "deleted_files = 0\n", "for file_name in excluded_files:\n", "    file_path = os.path.join(wavs_dir, file_name + '.wav')\n", "    try:\n", "        os.remove(file_path)\n", "        deleted_files += 1\n", "    except OSError as e:\n", "        print(f'Error deleting file {file_path}: {e}')\n", "\n", "#sanity check\n", "assert deleted_files == dropped\n", "print(f'deleted {deleted_files} wavs')"]}, {"cell_type": "markdown", "metadata": {"id": "cBPqKFffYGbY"}, "source": ["Create a tarball that you can download. Again, this might take a little while."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2024-04-22T15:57:45.611315Z", "iopub.status.busy": "2024-04-22T15:57:45.61089Z", "iopub.status.idle": "2024-04-22T15:59:50.216549Z", "shell.execute_reply": "2024-04-22T15:59:50.214817Z", "shell.execute_reply.started": "2024-04-22T15:57:45.611282Z"}, "id": "NkuS9ADsYGbY", "outputId": "71ac5b08-0519-4faf-80ad-a44f8116907e", "trusted": true}, "outputs": [], "source": ["!tar -czvf culled_dataset.tar.gz /workspace/output/*"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "eotl4i0aYGbY"}, "outputs": [], "source": []}], "metadata": {"colab": {"name": "Inspect and Clean TTS Dataset", "provenance": []}, "kaggle": {"accelerator": "none", "dataSources": [{"datasetId": 4372173, "sourceId": 7507317, "sourceType": "datasetVersion"}], "dockerImageVersionId": 30698, "isGpuEnabled": false, "isInternetEnabled": true, "language": "python", "sourceType": "notebook"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 0}